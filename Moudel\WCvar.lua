
-- 魔兽世界常用CVar批量设置插件
WanTiny_RegisterModule("WCvar", function()
    -- 导出CVar设置数据供UI使用
    local CVarSettings = {
        -- CVar设置数据
    }
    
    -- 将数据暴露给全局以供UI使用
    _G.WanTiny_CVarSettings = {
        {
            cvar = "cameraDistanceMaxZoomFactor",
            name = "镜头视距",
            nameColor = {0.2, 0.6, 1}, -- 蓝色
            description = "控制摄像机可以拉多远,我不确定最远是4还是5！",
            default = 4,
            min = 1,
            max = 4,
            step = 0.1,
            lowText = "最小",
            highText = "最大"
        },
        {
            cvar = "nameplateMaxDistance",
            name = "血条显示距离",
            nameColor = {1, 0.2, 0.2}, -- 红色
            description = "多远距离显示敌人血条",
            default = 41,
            min = 2,
            max = 41,
            step = 1,
            lowText = "最近",
            highText = "最远"
        },
        {
            cvar = "ffxDeath",
            name = "死亡迷雾",
            nameColor = {0.7, 0.7, 0.7}, -- 灰色
            description = "死亡时的泛光视觉效果",
            default = 1,
            min = 0,
            max = 1,
            step = 1,
            lowText = "彩色",
            highText = "迷雾"
        },
        {
            cvar = "ffxGlow",
            name = "全屏泛光特效",
            nameColor = {1, 1, 0.3}, -- 黄色
            description = "屏幕边缘光晕效果",
            default = 0,
            min = 0,
            max = 1,
            step = 1,
            lowText = "关闭",
            highText = "开启"
        },
        {
            cvar = "weatherdensity",
            name = "天气密度",
            nameColor = {0.3, 0.8, 0.3}, -- 绿色
            description = "天气效果的浓度,0：无天气效果，1：晴天，2：小雨，3：大雨",
            default = 1,
            min = 0,
            max = 3,
            step = 1,
            lowText = "晴天",
            highText = "大雨"
        },
        {
            cvar = "chatClassColorOverride",
            name = "聊天聊天着色",
            nameColor = {0.8, 0.5, 1}, -- 紫色
            description = "聊天中显示职业颜色,WLK中似乎没有太大意义！我不太确认暂时保留。",
            default = 1,
            min = 0,
            max = 1,
            step = 1,
            lowText = "关闭",
            highText = "开启"
        },
        {
            cvar = "violenceLevel",
            name = "暴力等级",
            nameColor = {1, 0.5, 0.2}, -- 橙色
            description = "血腥效果等级",
            default = 3,
            min = 0,
            max = 5,
            step = 1,
            lowText = "最小",
            highText = "最大"
        },
        -- ...existing code...
        {
            cvar = "alwaysCompareItems",
            name = "装备自动对比",
            nameColor = {0.2, 1, 1}, -- 青色
            description = "鼠标悬停自动对比装备",
            default = 1,
            min = 0,
            max = 1,
            step = 1,
            lowText = "关闭",
            highText = "开启"
        },
        {
            cvar = "floatingCombatTextCombatDamage",
            name = "伤害数字",
            nameColor = {1, 0.8, 0.2}, -- 金色
            description = "显示飘动的伤害数字",
            default = 1,
            min = 0,
            max = 1,
            step = 1,
            lowText = "关闭",
            highText = "开启"
        },
        {
            cvar = "WorldTextScale",
            name = "战斗数字缩放",
            nameColor = {0.7, 1, 0.7}, -- 浅绿
            description = "伤害数字等的大小",
            default = 1,
            min = 0.5,
            max = 9,
            step = 0.1,
            lowText = "最小",
            highText = "最大"
        },
        {
            cvar = "floatingCombatTextFloatMode",
            name = "浮动方向",
            nameColor = {0.9, 0.6, 0.1}, -- 橙黄
            description = "需要在设置-战斗-开启浮动战斗信息打勾.控制战斗信息（如治疗/获得法力等）滚动方式：1=下往上，2=上往下，3=弧形",
            default = 3,
            min = 1,
            max = 3,
            step = 1,
            lowText = "下→上",
            highText = "弧形"
        },
        {
            cvar = "scriptErrors",
            name = "脚本错误显示",
            nameColor = {1, 0.3, 0.3}, -- 红色
            description = "显示Lua错误提示",
            default = 1,
            min = 0,
            max = 1,
            step = 1,
            lowText = "关闭",
            highText = "开启"
        }
    }

end) -- 结束WCvar模块的初始化函数
