----------------------------------WaPlus综合功能模块----------------------------------
-- 包含：集结号快捷按钮、广告隐藏、团本CD、光环计时器、快捷焦点、好友染色、技能超距、任务价值、团队信息等功能

-- 统一模块注册系统
local function RegisterWaPlusModule(moduleName, initFunc)
    if WanTiny_RegisterModule then
        WanTiny_RegisterModule(moduleName, initFunc)
    else
        -- 单独调试用，统一1秒延时
        C_Timer.After(1, initFunc)
    end
end

----------------------------------下面是集结号快捷按钮功能模块----------------------------------

local function createStyledMultiLineEditBox(parent, width, height, opts)
    opts = opts or {}
    local frame = CreateFrame("Frame", nil, parent, BackdropTemplateMixin and "BackdropTemplate")
    frame:SetSize(width, height)
    if opts.point then
        frame:SetPoint(unpack(opts.point))
    end
    local borderData = {
        {tex = "Common-Input-Border-TL", point = "TOPLEFT", x = 0, y = 0, w = 8, h = 8},
        {tex = "Common-Input-Border-TR", point = "TOPRIGHT", x = 0, y = 0, w = 8, h = 8},
        {tex = "Common-Input-Border-BL", point = "BOTTOMLEFT", x = 0, y = 0, w = 8, h = 8},
        {tex = "Common-Input-Border-BR", point = "BOTTOMRIGHT", x = 0, y = 0, w = 8, h = 8},
        {tex = "Common-Input-Border-T", point = "TOPLEFT", x = 8, y = 0, isEdge = true, anchor = "TOP"},
        {tex = "Common-Input-Border-B", point = "BOTTOMLEFT", x = 8, y = 0, isEdge = true, anchor = "BOTTOM"},
        {tex = "Common-Input-Border-L", point = "TOPLEFT", x = 0, y = -8, isEdge = true, anchor = "LEFT"},
        {tex = "Common-Input-Border-R", point = "TOPRIGHT", x = 0, y = -8, isEdge = true, anchor = "RIGHT"},
    }
    for _, border in ipairs(borderData) do
        local tex = frame:CreateTexture(nil, "BACKGROUND")
        tex:SetTexture("Interface\\COMMON\\" .. border.tex)
        if border.isEdge then
            if border.anchor == "TOP" or border.anchor == "BOTTOM" then
                tex:SetPoint(border.anchor .. "LEFT", 8, 0)
                tex:SetPoint(border.anchor .. "RIGHT", -8, 0)
                tex:SetHeight(8)
            else
                tex:SetPoint("TOP" .. border.anchor, 0, -8)
                tex:SetPoint("BOTTOM" .. border.anchor, 0, 8)
                tex:SetWidth(8)
            end
        else
            tex:SetPoint(border.point, border.x, border.y)
            tex:SetSize(border.w, border.h)
        end
    end
    local M = frame:CreateTexture(nil, "BACKGROUND")
    M:SetTexture("Interface\\COMMON\\Common-Input-Border-M")
    M:SetPoint("TOPLEFT", 8, -8)
    M:SetPoint("BOTTOMRIGHT", -8, 8)
    if opts.bgColor then
        frame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background"})
        frame:SetBackdropColor(unpack(opts.bgColor))
    end
    local scroll = CreateFrame("ScrollFrame", nil, frame, "UIPanelScrollFrameTemplate")
    scroll:SetPoint("TOPLEFT", 0, 0)
    scroll:SetPoint("BOTTOMRIGHT", -26, 5)
    local editBox = CreateFrame("EditBox", nil, scroll)
    editBox:SetMultiLine(true)
    editBox:SetAutoFocus(false)
    editBox:SetFontObject(ChatFontNormal)
    editBox:SetWidth(width-28)
    editBox:SetHeight(height-16)
    editBox:SetMaxBytes(opts.maxBytes or 128)
    editBox:SetTextInsets(4, 4, 4, 4)
    editBox:SetJustifyH("LEFT")
    editBox:SetJustifyV("TOP")
    editBox:SetAltArrowKeyMode(false)
    editBox:EnableMouse(true)
    editBox:SetSpacing(4)
    scroll:SetScrollChild(editBox)
    scroll:EnableMouseWheel(true)
    scroll:SetScript("OnMouseWheel", function(self, delta)
        local current = self:GetVerticalScroll()
        local step = 20
        local min, max = 0, self:GetVerticalScrollRange()
        local new = math.max(min, math.min(max, current - delta * step))
        self:SetVerticalScroll(new)
    end)
    scroll:SetScript("OnSizeChanged", function(sf) editBox:SetWidth(sf:GetWidth() - 30) end)
    return frame, scroll, editBox
end

-- 工具函数和数据库
local function getDefaultComment(activityName)
    return "《晚妹》" .. activityName .. "无团双速通,来T/N/DPS 无PDD,无团双,装备135起,考核65% ！来的小伙伴带装等MMM....."
end

local function getMeetingHorn()
    local AceAddon = LibStub and LibStub("AceAddon-3.0", true)
    return AceAddon and AceAddon:GetAddon("MeetingHorn", true)
end

-- 统一的数据库初始化函数
local function initWanTinyDB()
    if not _G.WanTinyDB then
        _G.WanTinyDB = {}
    end
    if not _G.WanTinyDB.QuickButton then
        _G.WanTinyDB.QuickButton = {}
    end
    if not _G.WanTinyDB.Welcome then
        _G.WanTinyDB.Welcome = { enabled = false, channel = "WHISPER", text = "" }
    end
end

local function getQuickButtonDB() 
    initWanTinyDB()
    return _G.WanTinyDB.QuickButton
end

local function getWelcomeConfig() 
    initWanTinyDB()
    return _G.WanTinyDB.Welcome
end

local TOC_MODES = {
    {text = "10PTTOC", activityId = 13, modeId = 1}, {text = "10人HTOC", activityId = 14, modeId = 2},
    {text = "25PTTOC", activityId = 11, modeId = 1}, {text = "25人HTOC", activityId = 12, modeId = 2},
}
local ICC_MODES = {
    {text = "10PTICC", activityId = 19, modeId = 1}, {text = "10人HICC", activityId = 22, modeId = 2},
    {text = "25PTICC", activityId = 17, modeId = 1}, {text = "25人HICC", activityId = 18, modeId = 2},
}
local RS_MODES = {
    {text = "10PT红玉", activityId = 23, modeId = 1}, {text = "10人H红玉", activityId = 24, modeId = 2},
    {text = "25PT红玉", activityId = 21, modeId = 1}, {text = "25人H红玉", activityId = 22, modeId = 2},
    {text = "10人宝库", activityId = 8, modeId = 1}, {text = "25人宝库", activityId = 7, modeId = 1},
}
local P1P2_MODES = {
    {text = "NAXX 10人", activityId = 2, modeId = 1}, {text = "NAXX 25人", activityId = 1, modeId = 1},
    {text = "ULD 10人", activityId = 10, modeId = 1}, {text = "ULD 25人", activityId = 9, modeId = 1},
}

local createdButtons = {}

-- 统一的ESC处理函数
local function setupEscapeHandler(frame, onEscape)
    frame:EnableKeyboard(true)
    frame:SetScript("OnKeyDown", function(self, key)
        if key == "ESCAPE" then
            if onEscape then onEscape(self) else self:Hide() end
        end
    end)
    frame:SetPropagateKeyboardInput(false)
end

-- 为EditBox设置ESC处理
local function setupEditBoxEscape(editBox, onEscape)
    editBox:SetScript("OnEscapePressed", function(self)
        if onEscape then onEscape(self) else self:ClearFocus() end
    end)
end

-- 欢迎语设置
local function showWelcomeConfig(anchor)
    if WaPlusWelcomeFrame and WaPlusWelcomeFrame:IsShown() then 
        WaPlusWelcomeFrame:Hide() 
        return 
    end
    
    if not WaPlusWelcomeFrame then
        local f = CreateFrame("Frame", "WaPlusWelcomeFrame", UIParent, BackdropTemplateMixin and "BackdropTemplate")
        _G.WaPlusWelcomeFrame = f
        f:SetSize(340, 140)
        f:SetFrameStrata("TOOLTIP")
        f:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background", edgeFile = "Interface/Tooltips/UI-Tooltip-Border", tile = true, tileSize = 16, edgeSize = 16, insets = {left = 4, right = 4, top = 4, bottom = 4}})
        f:SetBackdropColor(1,1,1,1)
        f:EnableMouse(true)
        f:SetMovable(true)
        f:RegisterForDrag("LeftButton")
        f:SetScript("OnDragStart", f.StartMoving)
        f:SetScript("OnDragStop", f.StopMovingOrSizing)
        local title = f:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        title:SetPoint("TOP", 0, -10)
        title:SetText("进组欢迎语设置")
        local enableBtn = CreateFrame("CheckButton", nil, f, "ChatConfigCheckButtonTemplate")
        enableBtn:SetSize(20, 20)
        enableBtn:SetPoint("TOPLEFT", 20, -35)
        enableBtn.Text:SetText("启用欢迎语")
        enableBtn.Text:ClearAllPoints()
        enableBtn.Text:SetPoint("LEFT", enableBtn, "RIGHT", 2, 2)
        enableBtn:SetHitRectInsets(0, 0, 0, 0)
        -- 频道单选按钮组（绝对定位，横向等距）
        local channelConfig = {
            labels = { {"|cFFE066FF密语|r","WHISPER"}, {"|cFFFFA500团队|r","RAID"}, {"|cFF66B3FF小队|r","PARTY"} },
            btns = {}, baseX = 120, baseY = -35, spacing = 70
        }
        -- 启用/禁用频道选择（只控制可点，不再动态变灰，颜色由颜色码决定）
        local function updateChannelEnable()
            local enabled = enableBtn:GetChecked()
            for _, b in ipairs(channelConfig.btns) do b:EnableMouse(enabled) end
        end
        enableBtn:SetScript("OnClick", function(self)
            getWelcomeConfig().enabled = self:GetChecked()
            updateChannelEnable()
        end)
        for i, v in ipairs(channelConfig.labels) do
            local btn = CreateFrame("CheckButton", nil, f, "ChatConfigCheckButtonTemplate")
            btn:SetSize(20, 20)
            btn:SetPoint("TOPLEFT", f, "TOPLEFT", channelConfig.baseX + (i-1)*channelConfig.spacing, channelConfig.baseY)
            btn.Text:ClearAllPoints(); btn.Text:SetPoint("LEFT", btn, "RIGHT", 2, 2)
            btn.Text:SetText(v[1])
            btn:SetHitRectInsets(0, 0, 0, 0)
            btn:SetScript("OnClick", function(self)
                if not self:GetChecked() then self:SetChecked(true) return end
                getWelcomeConfig().channel = v[2]
                for j, b in ipairs(channelConfig.btns) do if b ~= self then b:SetChecked(false) end end
            end)
            channelConfig.btns[i] = btn
        end
        enableBtn:HookScript("OnClick", updateChannelEnable)
        updateChannelEnable()
        -- 使用通用魔兽风格多行输入框工厂
        local inputFrame, scroll, editBox = createStyledMultiLineEditBox(f, 300, 60, {point = {"TOPLEFT", enableBtn, "BOTTOMLEFT", 0, 0}})
        editBox:SetScript("OnTextChanged", function(self)
            local text = self:GetText()
            if #text > 128 then
                text = string.sub(text, 1, 128)
                self:SetText(text)
                self:SetCursorPosition(128)
            end
            getWelcomeConfig().text = text
        end)
        editBox:SetScript("OnKeyDown", function(self, key)
            if key == "ENTER" then self:Insert("\n") end
        end)
        local closeBtn = CreateFrame("Button", nil, f, "UIPanelCloseButton")
        closeBtn:SetPoint("TOPRIGHT", 2, 2)
        closeBtn:SetScript("OnClick", function() f:Hide() end)
        -- 设置ESC键处理
        setupEscapeHandler(f)
        -- 输入框ESC键处理
        setupEditBoxEscape(editBox, function(self)
            self:ClearFocus()
            f:Hide()
        end)
        f.enableBtn, f.channelBtns, f.editBox = enableBtn, channelConfig.btns, editBox
    end
    
    local cfg = getWelcomeConfig()
    WaPlusWelcomeFrame.enableBtn:SetChecked(cfg.enabled)
    -- 设置频道单选状态
    if WaPlusWelcomeFrame.channelBtns then
        for i, v in ipairs({"WHISPER", "RAID", "PARTY"}) do
            WaPlusWelcomeFrame.channelBtns[i]:SetChecked(cfg.channel == v)
        end
    end
    WaPlusWelcomeFrame.editBox:SetText(cfg.text ~= "" and cfg.text or "让我们一起欢迎{class}，[{name}]，他的装等已经达到惊人的{ilvl}。让我们一起欢迎他加入我们！")
    -- 更新频道按钮状态
    local enabled = WaPlusWelcomeFrame.enableBtn:GetChecked()
    for _, b in ipairs(WaPlusWelcomeFrame.channelBtns or {}) do 
        b:EnableMouse(enabled) 
    end
    WaPlusWelcomeFrame:ClearAllPoints()
    WaPlusWelcomeFrame:SetPoint(anchor and anchor:IsVisible() and "BOTTOM" or "CENTER", anchor, anchor and "TOP" or nil, 0, anchor and 8 or 0)
    WaPlusWelcomeFrame:Show()
end

-- 弹出菜单
local function showMenu(anchor, creator, MODES, menuFrameName)
    if _G[menuFrameName] and _G[menuFrameName]:IsShown() then _G[menuFrameName]:Hide() return end
    local btnWidth = anchor:GetWidth() or 80
    local menuFrame = _G[menuFrameName]
    if not menuFrame then
        menuFrame = CreateFrame("Frame", menuFrameName, UIParent, BackdropTemplateMixin and "BackdropTemplate")
        menuFrame:SetFrameStrata("TOOLTIP")
        menuFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background", edgeFile = "Interface/Tooltips/UI-Tooltip-Border", tile = true, tileSize = 16, edgeSize = 16, insets = {left = 4, right = 4, top = 4, bottom = 4}})
        menuFrame:SetBackdropColor(0,0,0,0.95)
        menuFrame.buttons = {}
        menuFrame:EnableMouse(true)
        menuFrame:SetScript("OnHide", function(self) if self.bgClickFrame then self.bgClickFrame:Hide() end end)
        -- 添加键盘事件处理
        setupEscapeHandler(menuFrame)
        menuFrame.bgClickFrame = CreateFrame("Button", nil, UIParent)
        menuFrame.bgClickFrame:SetFrameStrata("TOOLTIP")
        menuFrame.bgClickFrame:SetAllPoints(UIParent)
        menuFrame.bgClickFrame:EnableMouse(true)
        menuFrame.bgClickFrame:SetScript("OnClick", function() menuFrame:Hide() end)
        menuFrame.bgClickFrame:Hide()
        -- 只创建一次编辑弹窗相关控件
        menuFrame.editFrame = CreateFrame("Frame", nil, UIParent, "BackdropTemplate")
        menuFrame.editFrame:SetSize(300, 60)
        menuFrame.editFrame:SetFrameStrata("TOOLTIP")
        -- 标题栏（移到editFrame外部）
        local titleLabelFrame = CreateFrame("Frame", nil, UIParent)
        titleLabelFrame:SetSize(300, 22)
        titleLabelFrame:SetFrameStrata("TOOLTIP")
        local titleLabel = titleLabelFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        titleLabel:SetPoint("LEFT", 10, 0)
        titleLabelFrame.text = titleLabel
        titleLabelFrame:Hide()
        menuFrame.titleLabelFrame = titleLabelFrame
        -- 使用通用魔兽风格多行输入框工厂，并确保inputFrame加入editFrame
        local inputFrame, scroll, editBox = createStyledMultiLineEditBox(menuFrame.editFrame, 292, 64)
        inputFrame:SetAllPoints(menuFrame.editFrame)
        menuFrame.editFrame.inputFrame = inputFrame
        menuFrame.editFrame.editBox = editBox
        local saveButton = CreateFrame("Button", nil, UIParent, "UIPanelButtonTemplate")
        saveButton:SetSize(60, 22)
        saveButton:SetText("保存")
        saveButton:SetFrameStrata("TOOLTIP")
        saveButton:SetScript("OnClick", function()
            if menuFrame.editFrame._curTextKey then
                getQuickButtonDB()[menuFrame.editFrame._curTextKey] = menuFrame.editFrame.editBox:GetText()
            end
            menuFrame.editFrame.editBox:ClearFocus()
            menuFrame.editFrame:Hide()
            saveButton:Hide()
            if menuFrame.titleLabelFrame then menuFrame.titleLabelFrame:Hide() end
            menuFrame.editFrame._curTextKey = nil
            menuFrame:Hide()
        end)
        menuFrame.editFrame.saveButton = saveButton
        editBox:SetScript("OnKeyDown", function(eb, key) if key == "ENTER" then eb:Insert("\n") end end)
        editBox:SetScript("OnCursorChanged", function(eb, ...) ScrollingEdit_OnCursorChanged(eb, ...) end)
        editBox:SetScript("OnUpdate", ScrollingEdit_OnUpdate)
        setupEditBoxEscape(editBox, function(eb)
            eb:ClearFocus(); menuFrame.editFrame:Hide(); saveButton:Hide(); if menuFrame.titleLabelFrame then menuFrame.titleLabelFrame:Hide() end; menuFrame.editFrame._curTextKey = nil
        end)
        editBox:SetScript("OnShow", function(eb) eb:SetFocus() end)
        editBox:SetScript("OnTextChanged", function(eb, userInput)
            local text = eb:GetText()
            if userInput and text ~= eb.prevText then ScrollingEdit_OnTextChanged(eb, scroll) end
            eb.prevText = text
        end)
        menuFrame.editFrame:SetScript("OnMouseDown", function() if not editBox:HasFocus() then editBox:SetFocus(); editBox:SetCursorPosition(#editBox:GetText()) end end)
        menuFrame.editFrame:Hide(); saveButton:Hide()
    end
    menuFrame:SetSize(btnWidth + 20, #MODES * 28 + 8)
    for i, v in ipairs(MODES) do
        local btn = menuFrame.buttons[i]
        if not btn then
            btn = CreateFrame("Button", nil, menuFrame, "UIPanelButtonTemplate")
            menuFrame.buttons[i] = btn
        end
        btn:SetSize(btnWidth, 24)
        btn:ClearAllPoints()
        btn:SetPoint("TOPLEFT", 10, -4 - (i-1)*28)
        btn:SetText(v.text)
        btn:RegisterForClicks("LeftButtonUp", "RightButtonUp")
        btn:SetScript("OnClick", function(self, button)
            local Activity = creator.Activity
            local Mode = creator.Mode
            local Comment = creator.Comment
            if button == "LeftButton" then
                Activity:SetValue(v.activityId)
                Mode:SetValue(v.modeId)
                Comment:SetText(getQuickButtonDB()[v.text] or getDefaultComment(v.text))
                menuFrame:Hide()
            elseif button == "RightButton" then
                -- 先关闭其它所有菜单的editFrame
                for _, name in ipairs({"WaPlusP1P2Menu", "WaPlusTocMenu", "WaPlusIccMenu", "WaPlusRsMenu"}) do
                    local mf = _G[name]
                    if mf and mf.editFrame and mf.editFrame:IsShown() then
                        mf.editFrame:Hide()
                        if mf.titleLabelFrame then mf.titleLabelFrame:Hide() end
                        if mf.editFrame.saveButton then mf.editFrame.saveButton:Hide() end
                        mf.editFrame._curTextKey = nil
                    end
                end
                local editFrame = menuFrame.editFrame
                local editBox = editFrame.editBox
                local saveButton = editFrame.saveButton
                editFrame._curTextKey = v.text
                editBox:SetText(getQuickButtonDB()[v.text] or getDefaultComment(v.text))
                local titleLabelFrame = menuFrame.titleLabelFrame
                if titleLabelFrame and titleLabelFrame.text then
                    titleLabelFrame.text:SetText(v.text .. "预设内容：")
                end
                editFrame:ClearAllPoints()
                if titleLabelFrame then titleLabelFrame:ClearAllPoints() end
                local mainPanel = getMeetingHorn() and getMeetingHorn().MainPanel
                if mainPanel then
                    if titleLabelFrame then
                        titleLabelFrame:SetPoint("BOTTOM", editFrame, "TOP", 0, 0)
                        titleLabelFrame:Show()
                    end
                    editFrame:SetPoint("TOP", mainPanel, "TOPRIGHT", -230, -23)
                    saveButton:ClearAllPoints()
                    saveButton:SetPoint("LEFT", editFrame, "RIGHT", 10, 0)
                    editFrame:Show(); saveButton:Show(); editBox:Show()
                    menuFrame:Hide()  -- 隐藏子菜单
                else
                    if titleLabelFrame then titleLabelFrame:Hide() end
                    editFrame:Hide(); saveButton:Hide(); editBox:Hide()
                end
            end
        end)
        btn:Show()
    end
    for i = #MODES+1, #menuFrame.buttons do menuFrame.buttons[i]:Hide() end
    menuFrame:ClearAllPoints()
    menuFrame:SetPoint("TOPLEFT", anchor, "BOTTOMLEFT", -10, 0)
    menuFrame:Show()
    menuFrame:SetFrameLevel(anchor:GetFrameLevel() + 10)
    if menuFrame.bgClickFrame then menuFrame.bgClickFrame:Show(); menuFrame.bgClickFrame:SetFrameLevel(menuFrame:GetFrameLevel() - 1) end
end

local function WaPlus_RefreshButtons()
    local MeetingHorn = getMeetingHorn()
    if not (MeetingHorn and MeetingHorn.MainPanel and MeetingHorn.MainPanel.Manage and MeetingHorn.MainPanel.Manage.Creator) then return end
    
    local creator = MeetingHorn.MainPanel.Manage.Creator
    if not (creator and creator.Activity and creator.Mode and creator.Comment) then return end
    
    for _, btn in ipairs(createdButtons) do
        if btn then
            btn:Hide()
            btn:SetParent(nil)
        end
    end
    wipe(createdButtons)
    
    local parent = creator:GetParent()
    local btns = {
        { key = "P1-P2", modes = P1P2_MODES, menu = "WaPlusP1P2Menu", x = 200 },
        { key = "TOC", modes = TOC_MODES, menu = "WaPlusTocMenu", x = 290 },
        { key = "ICC", modes = ICC_MODES, menu = "WaPlusIccMenu", x = 380 },
        { key = "摸奖", modes = RS_MODES, menu = "WaPlusRsMenu", x = 470 },
        { key = "喊话", x = 630, isShout = true },
    }
    
    -- 通用按钮创建函数
    local function createBtn(text, width, height, point, relPoint, x, y, onClick)
        local btn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
        btn:SetSize(width, height)
        btn:SetText(text)
        btn:SetPoint(point, parent, relPoint, x, y)
        btn:SetScript("OnClick", onClick)
        table.insert(createdButtons, btn)
        return btn
    end
    
    for _, info in ipairs(btns) do
        if info.isShout then
            createBtn("喊话", 80, 22, "BOTTOMLEFT", "BOTTOMLEFT", info.x, -22, function()
                local comment = creator.Comment:GetText()
                for i = 1, 8 do SendChatMessage(comment, "CHANNEL", nil, i) end
            end)
        else
            createBtn(info.key, 80, 22, "BOTTOMLEFT", "BOTTOMLEFT", info.x, -22, function(self) showMenu(self, creator, info.modes, info.menu) end)
        end
    end

    if creator.CommentLabel then
        local welcomeBtn = createBtn("欢迎语", 60, 22, "LEFT", "LEFT", 140, 0, function(self) showWelcomeConfig(self) end)
        welcomeBtn:SetPoint("LEFT", creator.CommentLabel, "LEFT", 140, 0)
    end
end

-- 挂钩和事件注册
local function hookPanel()
    local MeetingHorn = getMeetingHorn()
    if not (MeetingHorn and MeetingHorn.MainPanel and MeetingHorn.MainPanel.Manage) then return false end
    local manage = MeetingHorn.MainPanel.Manage
    if manage and not manage.WaPlusHooked then
        manage:HookScript("OnShow", WaPlus_RefreshButtons)
        manage.WaPlusHooked = true
        return true
    end
    return manage.WaPlusHooked or false
end

-- 欢迎语功能
local sended = {}
local welcomeFrame = CreateFrame("Frame")
welcomeFrame:RegisterEvent("CHAT_MSG_SYSTEM")
welcomeFrame:SetScript("OnEvent", function(self, event, msg)
    local cfg = getWelcomeConfig()
    if not cfg.enabled then return end
    
    local name = msg:match(ERR_RAID_MEMBER_ADDED_S:gsub("%%s", "(.+)")) or msg:match(ERR_JOINED_GROUP_S:gsub("%%s", "(.+)"))
    if not name or name == UnitName("player") or name == (UnitName("player").."-"..GetRealmName()) or sended[name] then return end
    
    sended[name] = true
    if cfg.text and cfg.text ~= "" then
        local class, ilvl = "", ""
        -- 获取职业
        if name and UnitClass then
            local shortName = name:match("^([^%-]+)")
            local classLoc, classEng = UnitClass(shortName or name)
            class = classLoc or "?"
        end
        -- 获取装等
        if GetAverageItemLevel then
            local avg, _ = GetAverageItemLevel()
            ilvl = avg and string.format("%.1f", avg) or "?"
        end
        local text = cfg.text
        text = text:gsub("{name}", name or "?")
        text = text:gsub("{class}", class or "?")
        text = text:gsub("{ilvl}", ilvl or "?")
        SendChatMessage(text, cfg.channel or "WHISPER", nil, name)
    end
    C_Timer.After(3, function() sended[name] = nil end)
end)

-- MHQuickButton模块初始化函数
local function initMHQuickButton()
    hookPanel()
    WaPlus_RefreshButtons()
end

-- 注册MHQuickButton模块
RegisterWaPlusModule("MHQuickButton", initMHQuickButton)
----------------------------------往下是隐藏集结号广告模块----------------------------------

local function hideBannerFrame()
    if BannerFrame then
        BannerFrame:Hide()
        BannerFrame.Show = function() end
    end
end

-- 恢复集结号广告显示
local function restoreBannerFrame()
    if BannerFrame then
        BannerFrame.Show = nil  -- 恢复原始Show方法
        -- 如果需要重新显示，可以调用原始逻辑
        if BannerFrame.originalShow then
            BannerFrame.Show = BannerFrame.originalShow
        end
    end
end

-- 初始化Banner隐藏功能
local function initBannerHider()
    -- 保存原始Show方法（如果存在）
    if BannerFrame and BannerFrame.Show and not BannerFrame.originalShow then
        BannerFrame.originalShow = BannerFrame.Show
    end
    hideBannerFrame()
end

-- 注册BannerHider模块
RegisterWaPlusModule("BannerHider", initBannerHider)
----------------------------------往下是显示团本CD功能模块----------------------------------

local function GetRaidLockStatus()
    local raids = {"纳克萨玛斯","永恒之眼","黑曜石圣殿","阿尔卡冯的宝库","奥杜尔","奥妮克希亚的巢穴","十字军的试炼","冰冠堡垒","红玉圣殿"}
    local locked = {}
    for _, r in ipairs(raids) do locked[r.." 10"], locked[r.." 25"] = false, false end
    for i=1,25 do
        local name,_,_,_,isLocked,_,_,isRaid,num = GetSavedInstanceInfo(i)
        if name and isRaid then locked[name.." "..num] = isLocked end
    end
    return raids, locked
end

local raidCDFrame
local function ShowRaidCD()
    if not RaidFrameNotInRaid then return end
    if not raidCDFrame then
        raidCDFrame = CreateFrame("Frame", nil, RaidFrameNotInRaid)
        raidCDFrame:SetSize(160, 400)
        raidCDFrame:SetPoint("TOPLEFT", RaidFrameNotInRaid, "TOPRIGHT", 0, 0)
        local bg = raidCDFrame:CreateTexture(nil, "BACKGROUND")
        bg:SetAllPoints(true)
        bg:SetColorTexture(0,0,0,0.5)
        raidCDFrame.lines = {}
    end
    for _, line in ipairs(raidCDFrame.lines) do line:Hide() end
    local raids, locked = GetRaidLockStatus()
    local y, idx = 0, 1
    for _, size in ipairs({"10", "25"}) do
        local line = raidCDFrame.lines[idx] or raidCDFrame:CreateFontString(nil, "OVERLAY")
        raidCDFrame.lines[idx] = line
        line:ClearAllPoints()
        line:SetPoint("TOPLEFT", 0, -y)
        line:SetFontObject(GameFontNormalLarge)
        line:SetFont(select(1, line:GetFont()), 24, select(3, line:GetFont()))
        line:SetText("|cFF00e4ff"..size.."人团本|r")
        line:Show()
        y, idx = y + 24, idx + 1
        for _, r in ipairs(raids) do
            line = raidCDFrame.lines[idx] or raidCDFrame:CreateFontString(nil, "OVERLAY")
            raidCDFrame.lines[idx] = line
            line:ClearAllPoints()
            line:SetPoint("TOPLEFT", 0, -y)
            line:SetFontObject(GameFontNormal)
            line:SetFont(select(1, line:GetFont()), 18, select(3, line:GetFont()))
            local c = locked[r.." "..size] and "|cFF07ff00" or "|cFFffffff"
            line:SetText(c..r.."|r")
            line:Show()
            y, idx = y + 18, idx + 1
        end
    end
end

-- 自动隐藏/恢复CD显示：当RaidInfoFrame显示时隐藏raidCDFrame，关闭时恢复
local function TryHookRaidInfoFrame()
    if not RaidInfoFrame or RaidInfoFrame.WaPlusHooked then return end
    RaidInfoFrame:HookScript("OnShow", function() if raidCDFrame then raidCDFrame:Hide() end end)
    RaidInfoFrame:HookScript("OnHide", function()
        if RaidFrameNotInRaid and RaidFrameNotInRaid:IsShown() and raidCDFrame then
            ShowRaidCD()
            raidCDFrame:Show()
        end
    end)
    RaidInfoFrame.WaPlusHooked = true
end

local function hookRaidCD()
    if not RaidFrameNotInRaid then return end
    RaidFrameNotInRaid:HookScript("OnShow", ShowRaidCD)
    RaidFrameNotInRaid:HookScript("OnHide", function() if raidCDFrame then raidCDFrame:Hide() end end)
    C_Timer.After(0.5, TryHookRaidInfoFrame) -- 延迟尝试挂钩RaidInfoFrame，兼容加载顺序
end

-- RaidCD模块初始化函数（带条件检查）
local function initRaidCD()
    local addon = getMeetingHorn()
    if (type(WanTinyDB)=="table" and WanTinyDB.config and WanTinyDB.config.enabledModules and WanTinyDB.config.enabledModules.RaidCD) or (addon and addon.IsModuleEnabled and addon:IsModuleEnabled("RaidCD")) then
        hookRaidCD()
    end
end

-- 注册RaidCD模块
RegisterWaPlusModule("RaidCD", initRaidCD)

----------------------------------往下是光环计时器功能模块----------------------------------

-- 在模块外定义这个函数，这样在XML加载时可以找到它
function BuffTimers_OnLoad(self)
    -- 这个函数会被XML的OnLoad事件调用
    -- 但我们不在这里做任何事情，所有初始化都由模块系统处理
    -- 函数体为空，但函数必须存在以避免XML报错
end

-- BuffTimers模块功能实现
local function initBuffTimers()
    -- Local variables
    local lWarned

    -- Internal functions
    local function Bufftimes(seconds)
        if ( seconds > 86400 ) then
            local d = floor(seconds / 86400)
            return format("|cff00ff00%0.0fd|r", d)
        elseif ( seconds > 3600 ) then
            local h = floor((seconds % 86400 / 3600)*10 + 0.5) / 10
            return format("|cff00ff00%0.1fh|r", h)
        elseif ( seconds > 600 ) then
            local m = floor(seconds / 60)
            return format("|cff00ff00%dm|r", m)
        elseif ( seconds > 60 ) and ( seconds <= 600 ) then
            local m = floor(seconds / 60)
            local s = seconds % 60
            return format("|cff00ff00%d:%02d|r", m,s)
        elseif ( seconds > 0 ) then
            local s = seconds % 60
            return format("|cffffff00%01ds|r", s)
        end
        return ""
    end

    local SecondsToTimeAbbrev = Bufftimes
    _G.SecondsToTimeAbbrev = SecondsToTimeAbbrev

    -- OnFoo functions
    local function BT_AuraButton_Update(buttonName, index, filter)
        local unit = PlayerFrame.unit
        local name, rank, texture, count, debuffType, duration, expirationTime = UnitAura(unit, index, filter)

        local buffName = buttonName..index
        local buffDuration = getglobal(buffName.."Duration")

        if ( duration == 0 ) then
            buffDuration:SetText("|cff00ff00N/A|r")
            buffDuration:SetFont("Fonts\\ARHei.TTF", 14) -- 没有持续时间显示N/A的字体及大小
            buffDuration:Show()
        end
    end

    local function BT_AuraButton_UpdateDuration(buffButton, timeLeft)
        if( SHOW_BUFF_DURATIONS == "1" ) then
            local duration = getglobal(buffButton:GetName().."Duration")
            if( timeLeft ) then
                lSetTimeText(duration, timeLeft)
                duration:Show()
            else
                duration:Hide()
            end
        elseif( not lWarned ) then
            lWarned = true
        end
    end

    -- Halo Come from - 显示光环来源
    local classColors = {}
    do
        for class, c in pairs(CUSTOM_CLASS_COLORS or RAID_CLASS_COLORS) do
            classColors[class] = format('|cff%02x%02x%02x', c.r * 255, c.g * 255, c.b * 255)
        end
    end

    local function SetCaster(self, unit, index, filter)
        local name, icon, count, debuffType, duration, expirationTime, unitCaster, isStealable = UnitBuff(unit, index, filter)
        if unitCaster then
            local uname, urealm = UnitName(unitCaster)
            local _, uclass = UnitClass(unitCaster)
            if urealm then uname = uname .. '-' .. urealm end
            self:AddLine('|cff00ff00注：|r光环来自 ' .. '|cff00ff00[ |r' .. (classColors[uclass]) .. uname .. '|cff00ff00 ]|r')
            self:Show()
        end
    end

    -- 设置钩子
    hooksecurefunc(GameTooltip, 'SetUnitAura', SetCaster)
    hooksecurefunc(GameTooltip, 'SetUnitBuff', function(self, unit, index, filter)
        filter = filter and ('HELPFUL ' .. filter) or 'HELPFUL'
        SetCaster(self, unit, index, filter)
    end)
    hooksecurefunc(GameTooltip, 'SetUnitDebuff', function(self, unit, index, filter)
        filter = filter and ('HARMFUL ' .. filter) or 'HARMFUL'
        SetCaster(self, unit, index, filter)
    end)

    -- 在所有函数都定义好之后，再设置钩子
    hooksecurefunc("AuraButton_Update", BT_AuraButton_Update)
    hooksecurefunc("AuraButton_UpdateDuration", BT_AuraButton_UpdateDuration)
end

-- 注册BuffTimers模块
RegisterWaPlusModule("BuffTimers", initBuffTimers)

----------------------------------往下是快捷焦点功能模块----------------------------------

-- Focuser模块功能实现
local function initFocuser()
    local modifier = "shift" -- shift, alt or ctrl 设置习惯按键修饰符
    local mouseButton = "1" -- 1 = left, 2 = right, 3 = middle, 4 and 5 = thumb buttons if there are any 鼠标按键编号

    local function SetFocusHotkey(frame)
        if frame then
            frame:SetAttribute(modifier.."-type"..mouseButton,"focus")
        end
    end

    local function CreateFrame_Hook(type, name, parent, template)
        if template == "SecureUnitButtonTemplate" and name and _G[name] then
            SetFocusHotkey(_G[name])
        end
    end

    hooksecurefunc("CreateFrame", CreateFrame_Hook)

    -- Keybinding override so that models can be shift/alt/ctrl+clicked
    local f = CreateFrame("CheckButton", "FocuserButton", UIParent, "SecureActionButtonTemplate")
    f:SetAttribute("type1","macro")
    f:SetAttribute("macrotext","/focus mouseover")
    SetOverrideBindingClick(FocuserButton,true,modifier.."-BUTTON"..mouseButton,"FocuserButton")

    -- Set the keybindings on the default unit frames since we won't get any CreateFrame notification about them
    local duf = {
        PlayerFrame, PetFrame, TargetFrame, TargetofTargetFrame,
        PartyMemberFrame1, PartyMemberFrame2, PartyMemberFrame3, PartyMemberFrame4,
        PartyMemberFrame1PetFrame, PartyMemberFrame2PetFrame, PartyMemberFrame3PetFrame, PartyMemberFrame4PetFrame
    }

    -- 添加oUF框架支持（如果存在）
    local oufFrames = {
        "oUF_Player", "oUF_Pet", "oUF_Target", "oUF_TargetTarget", "oUF_Focus", "oUF_FocusTarget",
        "oUF_Boss1", "oUF_Boss2", "oUF_Boss3"
    }

    for _, frameName in ipairs(oufFrames) do
        if _G[frameName] then
            table.insert(duf, _G[frameName])
        end
    end

    -- 添加小队和团队框架支持
    for i = 1, 5 do
        local frames = {
            "oUF_PartyDPSUnitButton"..i, "oUF_PartyTargetDPSUnitButton"..i,
            "oUF_PartyUnitButton"..i, "oUF_PartyTargetUnitButton"..i,
            "oUF_Arena"..i, "oUF_Arena"..i.."Target"
        }
        for _, frameName in ipairs(frames) do
            if _G[frameName] then
                table.insert(duf, _G[frameName])
            end
        end
    end

    -- 添加团队框架支持
    for i = 1, 5 do
        for j = 1, 5 do
            local frames = {"oUF_RaidHeal"..i.."UnitButton"..j, "oUF_RaidDPS"..i.."UnitButton"..j}
            for _, frameName in ipairs(frames) do
                if _G[frameName] then
                    table.insert(duf, _G[frameName])
                end
            end
        end
    end

    -- 为所有框架设置焦点热键
    for i, frame in pairs(duf) do
        SetFocusHotkey(frame)
    end
end

-- 注册Focuser模块
RegisterWaPlusModule("Focuser", initFocuser)

----------------------------------往下是好友列表染色功能模块----------------------------------

-- iColor模块功能实现
local function initIColor()
    local myName = UnitName("player")
    local myRace = UnitRace("player")
    local normal = NORMAL_FONT_COLOR
    local green = GREEN_FONT_COLOR
    local white = HIGHLIGHT_FONT_COLOR
    local defColor = FRIENDS_WOW_NAME_COLOR_CODE
    local _G = _G

    local BC = {}
    for k, v in pairs(LOCALIZED_CLASS_NAMES_MALE) do BC[v] = k end
    for k, v in pairs(LOCALIZED_CLASS_NAMES_FEMALE) do BC[v] = k end

    local function colorString(string, class)
        local color = class and RAID_CLASS_COLORS[class] or GetQuestDifficultyColor(string)
        return ("%s%s|r"):format(ConvertRGBtoColorString(color), string)
    end

    local function guildRankColor(index)
        local r, g, b = 1, 1, 1
        local pct = index / GuildControlGetNumRanks()
        if pct <= 1.0 and pct >= 0.5 then
            r, g, b = (1.0-pct)*2, 1, 0
        elseif pct >= 0 and pct < 0.5 then
            r, g, b = 1, pct*2, 0
        end
        return r, g, b
    end

    -- 公会成员列表染色
    hooksecurefunc("GuildStatus_Update", function()
        local guildOffset = FauxScrollFrame_GetOffset(GuildListScrollFrame)
        local myZone = GetRealZoneText()
        local name, rankIndex, level, zone, online, classFileName
        local color, zcolor, lcolor, r, g, b

        for i=1, GUILDMEMBERS_TO_DISPLAY, 1 do
            name, _, rankIndex, level, _, zone, _, _, online, _, classFileName = GetGuildRosterInfo(guildOffset + i)
            if not name then break end

            color = RAID_CLASS_COLORS[classFileName] or normal
            zcolor = zone == myZone and green or white
            lcolor = GetQuestDifficultyColor(level) or white
            r, g, b = guildRankColor(rankIndex)

            if online then
                _G["GuildFrameButton"..i.."Name"]:SetTextColor(color.r, color.g, color.b)
                _G["GuildFrameButton"..i.."Zone"]:SetTextColor(zcolor.r, zcolor.g, zcolor.b)
                _G["GuildFrameButton"..i.."Level"]:SetTextColor(lcolor.r, lcolor.g, lcolor.b)
                _G["GuildFrameButton"..i.."Class"]:SetTextColor(color.r, color.g, color.b)
                _G["GuildFrameGuildStatusButton"..i.."Name"]:SetTextColor(color.r, color.g, color.b)
                _G["GuildFrameGuildStatusButton"..i.."Rank"]:SetTextColor(r, g, b)
            else
                _G["GuildFrameButton"..i.."Name"]:SetTextColor(color.r/2, color.g/2, color.b/2)
                _G["GuildFrameButton"..i.."Zone"]:SetTextColor(zcolor.r/2, zcolor.g/2, zcolor.b/2)
                _G["GuildFrameButton"..i.."Level"]:SetTextColor(lcolor.r/2, lcolor.g/2, lcolor.b/2)
                _G["GuildFrameButton"..i.."Class"]:SetTextColor(color.r/2, color.g/2, color.b/2)
                _G["GuildFrameGuildStatusButton"..i.."Name"]:SetTextColor(color.r/2, color.g/2, color.b/2)
                _G["GuildFrameGuildStatusButton"..i.."Rank"]:SetTextColor(r/2, g/2, b/2)
            end
        end
    end)

    -- 好友列表染色
    local function updateFriends()
        local buttons = FriendsFrameFriendsScrollFrame.buttons
        local myZone = GetRealZoneText()

        for i = 1, #buttons do
            local nameText, infoText
            local button = buttons[i]
            if button:IsShown() then
                if button.buttonType == FRIENDS_BUTTON_TYPE_WOW then
                    local info = C_FriendList.GetFriendInfoByIndex(button.id)
                    if info and info.connected then
                        local name = colorString(info.name, BC[info.className])
                        local level = colorString(info.level)
                        local class = colorString(info.className, BC[info.className])
                        nameText = name .. ", Lv" .. level .. "  " .. class
                        if info.area and info.area == myZone then infoText = format("|cff00ff00%s|r", info.area) end
                    end
                elseif button.buttonType == FRIENDS_BUTTON_TYPE_BNET then
                    local _, presenceName, _, _, _, toonID, client, isOnline = BNGetFriendInfo(button.id)
                    if isOnline and client == BNET_CLIENT_WOW then
                        local _, toonName, _, _, _, _, _, class, _, zoneName, level = BNGetGameAccountInfo(toonID)
                        if presenceName and toonName then
                            level = colorString(level)
                            toonName = colorString(toonName, BC[class])
                            nameText = presenceName .. " " .. defColor .. "(Lv" .. level .. " " .. toonName .. defColor .. ")"
                        end
                        if zoneName and zoneName == myZone then infoText = format("|cff00ff00%s|r", zoneName) end
                    end
                end
            end
            if nameText then button.name:SetText(nameText) end
            if infoText then button.info:SetText(infoText) end
        end
    end

    hooksecurefunc(FriendsFrameFriendsScrollFrame, "update", updateFriends)
    hooksecurefunc("FriendsFrame_UpdateFriends", updateFriends)

    -- 查询列表染色
    hooksecurefunc("WhoList_Update", function()
        local whoDropdown = _G["WhoFrameDropdown"]
        if whoDropdown then
            local updater = whoDropdown.UpdateSelections or whoDropdown.Update
            if type(updater) == "function" then pcall(function() updater(whoDropdown) end) end
            local menuIndex = 1
            local text = (whoDropdown.GetText and whoDropdown:GetText()) or whoDropdown.text
            if text then
                if text:find("地区") then menuIndex = 1
                elseif text:find("公会") then menuIndex = 2
                elseif text:find("种族") then menuIndex = 3 end
            end
            local whoOffset = FauxScrollFrame_GetOffset(WhoListScrollFrame)
            local myInfo = { GetRealZoneText(), GetGuildInfo("player"), UnitRace("player") }
            for i = 1, WHOS_TO_DISPLAY do
                local info = C_FriendList.GetWhoInfo(whoOffset + i)
                if not info then break end
                local columnTable = { info.area, info.fullGuildName, info.raceStr }
                local nameColor = info.filename and RAID_CLASS_COLORS[info.filename] or normal
                _G["WhoFrameButton"..i.."Name"]:SetTextColor(nameColor.r, nameColor.g, nameColor.b)
                local levelColor = info.level and GetQuestDifficultyColor(info.level) or white
                _G["WhoFrameButton"..i.."Level"]:SetTextColor(levelColor.r, levelColor.g, levelColor.b)
                _G["WhoFrameButton"..i.."Level"]:SetFont(_G["WhoFrameButton"..i.."Level"]:GetFont(), 13)
                local variableColor = columnTable[menuIndex] == myInfo[menuIndex] and green or white
                _G["WhoFrameButton"..i.."Variable"]:SetTextColor(variableColor.r, variableColor.g, variableColor.b)
            end
        end
    end)

    -- 战场积分榜染色
    hooksecurefunc("WorldStateScoreFrame_Update", function()
        local isArena = IsActiveBattlefieldArena()
        local scrollOffset = FauxScrollFrame_GetOffset(WorldStateScoreScrollFrame)

        for i = 1, MAX_WORLDSTATE_SCORE_BUTTONS do
            local scoreButton = _G["WorldStateScoreButton"..i]
            local name, _, _, _, _, faction, _, _, classToken = GetBattlefieldScore(scrollOffset + i)
            if name and faction and classToken then
                local n, s = strsplit("-", name, 2)
                n = colorString(n, classToken)
                if n == myName then
                    n = "> " .. n .. " <"
                end
                if s then
                    if isArena then
                        n = n.."|cffffffff - |r"..(faction==0 and "|cff20ff20" or "|cffffd200")..s.."|r"
                    else
                        n = n.."|cffffffff - |r"..(faction==0 and "|cffff2020" or "|cff00aef0")..s.."|r"
                    end
                end
                scoreButton.name.text:SetText(n)
            end
        end
    end)
end

-- 注册iColor模块
RegisterWaPlusModule("iColor", initIColor)

----------------------------------往下是技能超距功能模块----------------------------------

-- WanOutOfRange模块功能实现
local function initWanOutOfRange()
    local Timer = {}
    local timers = {}
    local font = GameTooltipTextLeft1:GetFont()
    local minFontsize = 8
    local maxFontsize = 28

    local ButtonType = {
        {value = "AutoCastable", type = "Pet"    },
        {value = "HotKey",       type = "Action" },
        {value = "Stock",        type = "Item"   },
    }

    local iCCDB = {
        Action = { config = true, min = 2,  size = 20 },
        Pet    = { config = true, min = 3,  size = 18 },
        Item   = { config = true, min = 3,  size = 20 },
        Buff   = { config = true, max = 60, scale = 0.5 },
    }

    local function Timer_OnUpdate(self, elapsed)
        if not self.cd:IsVisible() then
            self:Hide()
        else
            if self.nextUpdate <= 0 then
                Timer.Update(self)
            else
                self.nextUpdate = self.nextUpdate - elapsed
            end
        end
    end

    local function Timer_Hide(self)
        self.nextUpdate = 0
        self.cd:SetAlpha(1)
    end

    local function GetButtonType(btn)
        local name = btn:GetName()
        if name then
            for _, index in ipairs(ButtonType) do
                if _G[name..index.value] then
                    return index.type
                end
            end
        end
        return "Buff"
    end

    local function GetFormattedTime(t)
        if t < 9 then
            return ceil(t), 1.2, 1, t-floor(t)>0.5 and 0.12 or 0.82, 0.12, 0.2
        elseif t < 60 then
            return ceil(t), 1, 1, 0.82, 0, t-floor(t)
        elseif t < 600 then
            return ceil(t/60).."m", 0.85, 0.8, 0.6, 0, t-floor(t)
        elseif t < 3600 then
            return ceil(t/60).."m", 0.7, 0.8, 0.6, 0, t%60
        elseif (t < 86400) then
            return ceil(t/3600).."h", 0.6, 0.6, 0.4, 0, t%3600
        else
            return ceil(t).."d", 0.6, 0.4, 0.4, 0.4, t%86400
        end
    end

    function Timer.Start(cd, start, duration)
        if cd:IsForbidden() then return end
        cd.button = cd.button or cd:GetParent()
        if cd.button then
            cd.type = cd.type or GetButtonType(cd.button)
            if cd.type then
                local size = iCCDB[cd.type].size or floor((iCCDB[cd.type].scale * cd.button:GetSize()) + 0.5)
                local fontSize = size > maxFontsize and maxFontsize or size
                if start > 0 and duration > (iCCDB[cd.type].min or 0) and fontSize >= minFontsize and iCCDB[cd.type].config then
                    local timer = timers[cd] or Timer.Create(cd)
                    if timer then
                        timer.start = start
                        timer.duration = duration
                        timer.fontSize = fontSize
                        timer.nextUpdate = 0
                        timer:Show()
                    end
                elseif timers[cd] then
                    timers[cd]:Hide()
                end
            end
        end
    end

    function Timer.Create(cd)
        local timer = CreateFrame("Frame", nil, cd.button)
        timer:SetAllPoints(cd)
        timer.cd = cd
        timer.type = cd.type
        timer.button = cd.button
        timer:Hide()
        timer:SetScript("OnUpdate", Timer_OnUpdate)
        timer:SetScript("OnHide", Timer_Hide)

        local text = timer:CreateFontString(nil, "OVERLAY")
        if cd.type == "Buff" then
            text:SetPoint("TOPRIGHT", timer, "TOPRIGHT", 3, 4)
        else
            text:SetPoint("CENTER", timer, "CENTER", 0, 1)
        end
        timer.text = text

        timers[cd] = timer
        return timer
    end

    function Timer.Update(timer)
        local time = timer.start + timer.duration - GetTime()
        local max = iCCDB[timer.type].max
        if max then
            if time > max and max > 0 then
                if timer.text:IsVisible() then
                    timer.text:Hide()
                end
                timer.cd:SetAlpha(1)
                return
            else
                if not timer.text:IsVisible() then
                    timer.text:Show()
                end
                timer.cd:SetAlpha(0)
            end
        end

        if timer.text:IsVisible() then
            local text, scale, r, g, b, nextUpdate = GetFormattedTime(time)
            timer.text:SetFont(font, timer.fontSize, "OUTLINE")
            timer.text:SetText(text)
            timer.text:SetTextColor(r, g, b)
            timer:SetScale(scale)
            timer.nextUpdate = nextUpdate
        end

        if time < 0.2 then
            timer:Hide()
            timer.cd:SetAlpha(1)
        end
    end

    -- 创建全局框架用于事件处理
    local Wan = CreateFrame("Frame", "WanOutOfRangeFrame", UIParent)
    _G.WanOutOfRangeFrame = Wan
    Wan:Hide()
    Wan:RegisterEvent("PLAYER_ENTERING_WORLD")
    Wan:SetScript("OnEvent", function()
        for cooldown, timer in ipairs(timers) do
            Timer.Update(timer)
        end
    end)

    -- Cooldown数字显示（怀旧服兼容）
    local meta = getmetatable(CreateFrame("Cooldown", nil, nil, "CooldownFrameTemplate"))
    if meta and meta.__index and meta.__index.SetCooldown then
        hooksecurefunc(meta.__index, "SetCooldown", Timer.Start)
    end

    -- 技能超距变红功能
    hooksecurefunc("ActionButton_UpdateRangeIndicator", function(self, checksRange, inRange)
        if string.find(self:GetName(), "PetAction") then return end
        local range = false
        if checksRange and not inRange then
            self.icon:SetVertexColor(0.5, 0.1, 0.1)
            range = true
        end
        if self.range ~= range and range == false then
            if self.UpdateUsable then
                self:UpdateUsable()
            elseif ActionButton_UpdateUsable then
                ActionButton_UpdateUsable(self)
            end
        end
        self.range = range
    end)
end

-- 注册WanOutOfRange模块
RegisterWaPlusModule("WanOutOfRange", initWanOutOfRange)

----------------------------------往下是任务奖励价值功能模块----------------------------------

-- QuestPrice模块功能实现
local function initQuestPrice()
    -- 装备类型文字映射
    local typeTexts = {
        ["INVTYPE_NECK"] = "项",
        ["INVTYPE_BODY"] = "衬",
        ["INVTYPE_FINGER"] = "戒",
        ["INVTYPE_TRINKET"] = "饰",
        ["INVTYPE_CLOAK"] = "披",
        ["INVTYPE_WEAPON"] = "单",
        ["INVTYPE_SHIELD"] = "盾",
        ["INVTYPE_2HWEAPON"] = "双",
        ["INVTYPE_WEAPONMAINHAND"] = "主",
        ["INVTYPE_WEAPONOFFHAND"] = "副",
        ["INVTYPE_HOLDABLE"] = "副",
        ["INVTYPE_RANGED"] = "远",
        ["INVTYPE_THROWN"] = "远",
        ["INVTYPE_RANGEDRIGHT"] = "远",
        ["INVTYPE_RELIC"] = "圣",
    }

    local CLASS_AMOR_TYPE = {
        ["WARRIOR"]     = '板',
        ["MAGE"]        = '布',
        ["ROGUE"]       = '皮',
        ["DRUID"]       = '皮',
        ["HUNTER"]      = '锁',
        ["SHAMAN"]      = '锁',
        ["PRIEST"]      = '布',
        ["WARLOCK"]     = '布',
        ["PALADIN"]     = '板',
        ["DEATHKNIGHT"] = '板',
    }
    local player_class = select(2, UnitClass'player')

    local function SetTypeText(link, button)
        local subTypeText = button.subTypeText
        if link then
            local class, subclass, _, slot = select(6, GetItemInfo(link))
            if class=="护甲" and subclass and slot~="INVTYPE_CLOAK" then
                subclass = subclass:sub(1,3)
                if subclass=="布" or subclass=="皮" or subclass=="锁" or subclass=="板" then
                    subTypeText:SetText(subclass)
                    if(subclass == CLASS_AMOR_TYPE[player_class]) then
                        subTypeText:SetTextColor(.1,.8,.1)
                    else
                        subTypeText:SetTextColor(1, 1, 1)
                    end
                    return
                end
            end
            if slot and typeTexts[slot] then
                subTypeText:SetText(typeTexts[slot])
                subTypeText:SetTextColor(.1,.7,1)
                return
            end
        end
        subTypeText:SetText("")
    end

    local function QuestPriceFrame_OnUpdate(self)
        local button = self:GetParent()
        button.subTypeText:SetText("")
        self = _G[button:GetName().."QuestPriceFrame"]
        if not button.rewardType or button.rewardType == "item" then
            local func = QuestInfoFrame.questLog and GetQuestLogItemLink or GetQuestItemLink
            local link = func(button.type, button:GetID())
            SetTypeText(link, button)
            local price = link and select(11, GetItemInfo(link))
            if price and price > 0 then
                MoneyFrame_Update(self, price)
                local _, _, _, offsetx, _ = _G[self:GetName().."CopperButtonText"]:GetPoint()
                _G[self:GetName().."GoldButtonText"]:SetPoint("RIGHT", offsetx, 0);
                _G[self:GetName().."SilverButtonText"]:SetPoint("RIGHT", offsetx, 0);
                _G[self:GetName().."CopperButtonText"]:SetPoint("RIGHT", offsetx, 0);
                self:Show()
            else
                self:Hide()
            end
        end
    end

    local function CreatePriceFrame(name)
        local button = _G[name]
        if button then
            local frame = CreateFrame("Frame", name.."QuestPriceFrame", button, "SmallMoneyFrameTemplate")
            frame:SetPoint("BOTTOMRIGHT", 10, 3)
            frame:Raise()
            frame:SetScale(0.85)
            MoneyFrame_SetType(frame, "STATIC")
            frame.button = button
            local text = _G[button:GetName().."Name"]
            text:SetPoint("LEFT", _G[button:GetName().."NameFrame"], 15, -3);
            text:SetJustifyV("TOP")
            hooksecurefunc(text, "SetText", QuestPriceFrame_OnUpdate)

            local ft = button:CreateFontString()
            ft:SetFont(ChatFontNormal:GetFont(), 12, "OUTLINE")
            ft:SetTextColor(.5,1,.5)
            ft:SetPoint("BOTTOMLEFT", 0, 4)
            button.subTypeText = ft
        end
    end

    -- 6.0后创建的按钮
    hooksecurefunc("QuestInfo_GetRewardButton", function(rewardsFrame, index)
        local rewardButtons = rewardsFrame == QuestInfoRewardsFrame and rewardsFrame.RewardButtons or nil;
        if ( rewardButtons and rewardButtons[index] and not rewardButtons[index].subTypeText) then
            CreatePriceFrame(rewardButtons[index]:GetName())
        end
    end)

    local function GetMostExpensiveChoice()
        local max = -1
        local chosenLink, chosenId
        for i=1, GetNumQuestChoices() do
            local link = GetQuestItemLink("choice", i)
            local _, _, quantity = GetQuestItemInfo('choice', i)
            if not link then return end --ItemCache issue
            local price = link and select(11, GetItemInfo(link)) or 0
            price = price * (quantity or 1)
            if price > max then
                max = price
                chosenLink = link
                chosenId = i
            end
            SetTypeText(link, _G["QuestInfoRewardsFrameQuestInfoItem"..i])
        end
        if max > 0 then
            return chosenLink, chosenId, max
        end
    end
end

-- 注册QuestPrice模块
RegisterWaPlusModule("QuestPrice", initQuestPrice)

----------------------------------往下是团队信息功能模块----------------------------------

-- RaidInfoFrame模块功能实现
local function initRaidInfoFrame()
    -- 只在怀旧服中运行
    if select(4, GetBuildInfo()) > 40000 then
        return
    end

    local _G = _G
    local GetNumSavedInstances, GetSavedInstanceInfo, GetSavedInstanceEncounterInfo = GetNumSavedInstances, GetSavedInstanceInfo, GetSavedInstanceEncounterInfo
    local GameTooltip = GameTooltip
    local SecondsToTime = SecondsToTime
    local RESETS_IN = RESETS_IN
    local BOSS_DEAD = BOSS_DEAD
    local BOSS_ALIVE = BOSS_ALIVE

    local hooked = {}
    local function HookButton(Button)
        if hooked[Button] == nil then
            hooked[Button] = true
            Button:SetScript("OnEnter", function(self)
                if self.raidIndex <= GetNumSavedInstances() then
                    local name, id, reset, difficulty, locked, extended, instanceIDMostSig, isRaid, maxPlayers, difficultyName, numEncounters, encounterProgress = GetSavedInstanceInfo(self.raidIndex)
                    if name ~= nil then
                        GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
                        GameTooltip:AddDoubleLine(name, id, 1.0, 1.0, 1.0, 0.35, 0.35, 0.35)
                        if locked then
                            GameTooltip:AddLine(RESETS_IN .. " " .. SecondsToTime(reset), 0.5, 0.5, 0.5)
                            GameTooltip:AddLine(" ")
                            for encounterIndex = 1, numEncounters do
                                local bossName, fileDataID, isKilled, unknown4 = GetSavedInstanceEncounterInfo(self.raidIndex, encounterIndex)
                                if isKilled then
                                    GameTooltip:AddDoubleLine(bossName, BOSS_DEAD, 0.875, 0.875, 0.875, 1.0, 0.0, 0.0)
                                else
                                    GameTooltip:AddDoubleLine(bossName, BOSS_ALIVE, 0.875, 0.875, 0.875, 0.0, 1.0, 0.0)
                                end
                            end
                        end
                        GameTooltip:Show()
                    end
                end
            end)
            Button:SetScript("OnLeave", function(self)
                GameTooltip:Hide()
            end)
        end
    end

    -- 注册滚动框回调
    if RaidInfoFrame and RaidInfoFrame.ScrollBox then
        RaidInfoFrame.ScrollBox:RegisterCallback(_G.ScrollBoxListViewMixin.Event.OnAcquiredFrame, function(owner, line, index)
            HookButton(line)
        end)
    end
end

-- 注册RaidInfoFrame模块
RegisterWaPlusModule("RaidInfoFrame", initRaidInfoFrame)