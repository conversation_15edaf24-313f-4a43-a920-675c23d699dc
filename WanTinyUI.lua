local AddonName, ns = ...

-- ==================== 常量定义 ====================
local WanTinyUI = {
    FRAME_WIDTH = 580,
    FRAME_HEIGHT = 500,
    borderAlpha = 0.5,

    -- 布局常量
    LAYOUT = {
        -- 基础间距
        PADDING = 20,           -- 基础边距
        SPACING = 30,           -- 元素间距
        ROW_HEIGHT = 30,        -- 标准行高
        SECTION_SPACING = 50,   -- 区块间距
        BUTTON_SPACING = 10,    -- 按钮间距

        -- 控件尺寸
        CHECKBOX_HEIGHT = 35,   -- 复选框容器高度
        SLIDER_HEIGHT = 35,     -- 滑块容器高度
        BUTTON_HEIGHT = 25,     -- 标准按钮高度

        -- 标准宽度
        CHECKBOX_WIDTH = 140,   -- 复选框标准宽度（包含文字和复选框本体）
        SLIDER_WIDTH = 280,     -- 滑块标准宽度（包含标签+按钮+滑块+数值显示）
        BUTTON_WIDTH = 80,      -- 标准按钮宽度

        -- 偏移量
        TEXT_OFFSET_Y = -2,     -- 文字垂直偏移
        CHECKBOX_TEXT_OFFSET_X = 27,  -- 复选框文字水平偏移

        -- 标签页布局
        TAB_PADDING = 5,        -- 标签页边距
        TAB_SPACING = 1,        -- 标签页间距
        TAB_BOTTOM_OFFSET = -28, -- 标签页底部偏移

        -- 特殊布局常量
        TITLE_SPACING = 20,     -- 标题和内容间距
        SECTION_GAP = 25,       -- 区块间隙
        EXTRA_SPACING = 15,     -- 额外间距
        ITEM_WIDTH_4COL = 130,  -- 4列布局项宽度
        LEFT_MARGIN = 20,       -- 左边距
        CVAR_BUTTON_SPACING = 100, -- CVar按钮间距
    }
}
ns.WanTinyUI = WanTinyUI

local COLORS = {
    GOLD = {1, 0.82, 0},
    WHITE = {1, 1, 1},
    GREEN = {0, 1, 0},
    BLUE = {0, 0.75, 1},
    WARNING = {1, 0.8, 0.2},
    SUBTITLE = {0, 0.75, 1}
}
COLORS.TITLE = COLORS.GOLD  -- 标题和金色相同

local FONTS = {
    NORMAL = {STANDARD_TEXT_FONT, 14, "OUTLINE"},
    LARGE = {STANDARD_TEXT_FONT, 16, "OUTLINE"},
    WARNING = {"Fonts\\FRIZQT__.TTF", 24, "OUTLINE"}
}

-- ==================== 工具函数 ====================

local function setFont(fontString, fontType)
    fontString:SetFont(unpack(FONTS[fontType] or FONTS.NORMAL))
end

-- 布局计算工具函数
local LayoutUtils = {}

-- 计算居中布局的起始X坐标
function LayoutUtils.GetCenteredStartX(parentWidth, itemWidth, itemCount, spacing)
    parentWidth = parentWidth or WanTinyUI.FRAME_WIDTH
    spacing = spacing or 0
    local totalWidth = itemCount * itemWidth + (itemCount - 1) * spacing
    return math.max(WanTinyUI.LAYOUT.PADDING, (parentWidth - totalWidth) / 2)
end

-- 计算网格布局位置
function LayoutUtils.GetGridPosition(index, cols, itemWidth, itemHeight, startX, startY, spacing)
    local col = (index - 1) % cols
    local row = math.floor((index - 1) / cols)
    local x = startX + col * (itemWidth + (spacing or 0))
    local y = startY - row * (itemHeight + (spacing or 0))
    return x, y
end

-- 获取父容器宽度的安全方法
function LayoutUtils.GetParentWidth(parent)
    return parent:GetWidth() or WanTinyUI.FRAME_WIDTH
end

-- 获取居中起始X坐标的便捷方法（合并了GetButtonCenterLayout功能）
function LayoutUtils.GetCenteredStartXSafe(parent, itemWidth, cols, spacing)
    spacing = spacing or WanTinyUI.LAYOUT.BUTTON_SPACING
    return LayoutUtils.GetCenteredStartX(LayoutUtils.GetParentWidth(parent), itemWidth, cols, spacing)
end

-- 安全获取全局变量的方法
function LayoutUtils.SafeGetGlobal(name)
    return _G[name]
end

-- 通用配置更新函数
function LayoutUtils.UpdateConfig(configTable, key, value, callbackTable)
    configTable[key] = value
    if callbackTable and callbackTable[key] then
        callbackTable[key](value)
    end
end

-- ==================== 布局工具 ====================

-- 将布局工具赋值给WanTinyUI
WanTinyUI.LayoutUtils = LayoutUtils

-- ==================== 工具函数 ====================

local function setStandardBackdrop(frame, bgAlpha, borderAlpha)
    local r, g, b = unpack(WanTinyUI.GetClassRGB())
    frame:SetBackdrop({
        bgFile = "Interface/ChatFrame/ChatFrameBackground",
        edgeFile = "Interface/ChatFrame/ChatFrameBackground",
        edgeSize = 1
    })
    frame:SetBackdropColor(0, 0, 0, bgAlpha or 0.1)
    frame:SetBackdropBorderColor(r, g, b, borderAlpha or 0.5)
end

local function setButtonText(button, text, fontSize, color)
    local t = button:CreateFontString()
    t:SetAllPoints()
    t:SetFont(STANDARD_TEXT_FONT, fontSize or 14, "OUTLINE")
    t:SetTextColor(unpack(color or COLORS.WHITE))
    if text then t:SetText(text) end
    button:SetFontString(t)
    button.textObj = t
    return t
end

local function setTooltip(frame, title, desc, anchor)
    if not desc then return end
    local originalOnEnter = frame:GetScript("OnEnter")
    local originalOnLeave = frame:GetScript("OnLeave")

    frame:SetScript("OnEnter", function(self)
        if originalOnEnter then originalOnEnter(self) end
        GameTooltip:SetOwner(self, anchor or "ANCHOR_RIGHT")
        GameTooltip:SetText(title, unpack(COLORS.WHITE))
        GameTooltip:AddLine(desc, nil, nil, nil, true)
        GameTooltip:Show()
    end)

    frame:SetScript("OnLeave", function(self)
        GameTooltip:Hide()
        if originalOnLeave then originalOnLeave(self) end
    end)
end



-- 获取启用模块配置的通用函数
local function getEnabledModules()
    return _G.WanTinyDB and _G.WanTinyDB.config and _G.WanTinyDB.config.enabledModules
end

-- 模块检测配置
local MODULE_CONFIG = {
    ["Buttonstorage"] = function() return _G.WanTiny_MiniMapBut ~= nil end,
    ["WCvar"] = function() return _G.WanTiny_CVarSettings ~= nil and #_G.WanTiny_CVarSettings > 0 end,
    ["WanMenu"] = function()
        local enabledModules = getEnabledModules()
        return enabledModules and enabledModules.WanMenu ~= false and _G.WanMenu ~= nil
    end,
    ["WCombatTimes"] = function()
        local enabledModules = getEnabledModules()
        return enabledModules and enabledModules.WCombatTimes ~= false and _G.WCT ~= nil
    end,
    ["WChat"] = function()
        local enabledModules = getEnabledModules()
        return enabledModules and enabledModules.WChat ~= false and _G.WChatClassicDB ~= nil
    end
}

-- 通用模块加载检测函数
local function checkModuleLoaded(moduleName)
    return MODULE_CONFIG[moduleName] and MODULE_CONFIG[moduleName]() or false
end

-- 模块显示名称映射
local MODULE_NAMES = {
    ["Buttonstorage"] = "小地图按钮收纳模块",
    ["WCvar"] = "CVar设置数据",
    ["WanMenu"] = "快捷标记功能模块",
    ["WCombatTimes"] = "战斗计时器模块",
    ["WChat"] = "聊天增强模块"
}

-- 通用模块未加载提示文字创建函数
local function createModuleNotLoadedText(parent, moduleName, anchorPoint, relativeFrame, x, y)
    local displayName = MODULE_NAMES[moduleName] or (moduleName .. "模块")
    local moduleText = ("|cffffd200%s未加载|r\n\n|cff80c0ff请确保%s模块已启用\n或尝试重载界面(/reload)|r"):format(displayName, moduleName)
    local loadingText = parent:CreateFontString(nil, "OVERLAY")
    loadingText:SetPoint(anchorPoint or "CENTER", relativeFrame or parent, anchorPoint or "CENTER", x or 0, y or 0)
    setFont(loadingText, "WARNING")
    loadingText:SetTextColor(unpack(COLORS.WARNING))
    loadingText:SetText(moduleText)
    return loadingText
end

-- ==================== UI控件创建 ====================


local function CreateLabeledCheckButton(parent, label, x, y, checked, onClick, tooltip)
    local container = CreateFrame("Frame", nil, parent)
    container:SetSize(WanTinyUI.LAYOUT.CHECKBOX_WIDTH, WanTinyUI.LAYOUT.CHECKBOX_HEIGHT)
    container:SetPoint("TOPLEFT", parent, "TOPLEFT", x, y)

    local btn = CreateFrame("CheckButton", nil, container, "UICheckButtonTemplate")
    btn:SetSize(22, 22)
    btn:SetPoint("LEFT", container, "LEFT", 0, 0)
    btn:SetChecked(checked)
    btn:SetScript("OnClick", onClick)

    local text = container:CreateFontString(nil, "ARTWORK")
    setFont(text, "LARGE")
    text:SetPoint("LEFT", container, "LEFT", WanTinyUI.LAYOUT.CHECKBOX_TEXT_OFFSET_X, WanTinyUI.LAYOUT.TEXT_OFFSET_Y)
    text:SetText(label)
    text:SetTextColor(unpack(COLORS.WHITE))

    if tooltip then setTooltip(container, label, tooltip) end

    -- 兼容性方法
    container.checkButton = btn
    container.SetChecked = function(self, checked) btn:SetChecked(checked) end
    container.GetChecked = function(self) return btn:GetChecked() end
    return container
end



local function CreateLabeledSlider(parent, label, x, y, min, max, step, value, onChanged, valueFormat, tooltip, options)
    options = options or {}
    local container = CreateFrame("Frame", nil, parent)
    container:SetSize(WanTinyUI.LAYOUT.SLIDER_WIDTH, options.verticalLayout and 50 or WanTinyUI.LAYOUT.SLIDER_HEIGHT)
    container:SetPoint("TOPLEFT", parent, "TOPLEFT", x, y)

    local lbl = container:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    lbl:SetPoint("LEFT", container, "LEFT", 0, 0)
    lbl:SetText(label)
    lbl:SetFont(STANDARD_TEXT_FONT, 14, "OUTLINE")
    lbl:SetTextColor(unpack(options.labelColor or COLORS.WHITE))

    local leftBtn = CreateFrame("Button", nil, container)
    leftBtn:SetPoint("LEFT", lbl, "RIGHT", 8, 0)
    leftBtn:SetSize(13, 19)
    leftBtn:SetNormalAtlas("Minimal_SliderBar_Button_Left")

    local slider = CreateFrame("Slider", nil, container, "MinimalSliderTemplate")
    slider:SetPoint("LEFT", leftBtn, "RIGHT", 2, 0)
    slider:SetMinMaxValues(min, max)
    slider:SetValueStep(step)
    slider:SetValue(value)
    slider:SetSize(100, 20)

    local rightBtn = CreateFrame("Button", nil, container)
    rightBtn:SetPoint("LEFT", slider, "RIGHT", 2, 0)
    rightBtn:SetSize(13, 19)
    rightBtn:SetNormalAtlas("Minimal_SliderBar_Button_Right")

    local valueText = container:CreateFontString(nil, "OVERLAY")
    setFont(valueText, "LARGE")
    valueText:SetPoint("LEFT", rightBtn, "RIGHT", 8, 0)
    valueText:SetTextColor(unpack(COLORS.GOLD))
    valueText:SetText(valueFormat and valueFormat(value) or tostring(value))

    local function updateValue(newValue)
        newValue = math.max(min, math.min(max, newValue))
        slider:SetValue(newValue)
        valueText:SetText(valueFormat and valueFormat(newValue) or tostring(newValue))
        if onChanged then onChanged(slider, newValue) end
    end

    leftBtn:SetScript("OnClick", function() updateValue(slider:GetValue() - step) end)
    rightBtn:SetScript("OnClick", function() updateValue(slider:GetValue() + step) end)
    slider:SetScript("OnValueChanged", function(self, v)
        valueText:SetText(valueFormat and valueFormat(v) or tostring(v))
        if onChanged then onChanged(self, v) end
    end)

    slider.valueText = valueText
    slider.labelText = lbl  -- 添加标签引用
    if tooltip then setTooltip(container, label, tooltip) end
    return slider
end

local function CreateLabeledDropDown(parent, label, x, y, options, selected, onSelect, tooltip)
    local container = CreateFrame("Frame", nil, parent)
    container:SetSize(200, WanTinyUI.LAYOUT.CHECKBOX_HEIGHT)
    container:SetPoint("TOPLEFT", parent, "TOPLEFT", x, y)

    local lbl = container:CreateFontString(nil, "OVERLAY")
    setFont(lbl, "LARGE")
    lbl:SetPoint("LEFT", container, "LEFT", 0, WanTinyUI.LAYOUT.TEXT_OFFSET_Y)
    lbl:SetText(label)
    lbl:SetTextColor(unpack(COLORS.WHITE))

    local dropdown = CreateFrame("Frame", nil, container, "UIDropDownMenuTemplate")
    dropdown:SetPoint("LEFT", lbl, "RIGHT", 5, 0)
    UIDropDownMenu_SetWidth(dropdown, 70)

    UIDropDownMenu_Initialize(dropdown, function()
        for k, v in pairs(options) do
            local info = UIDropDownMenu_CreateInfo()
            info.text = v
            info.value = k
            info.func = function(self)
                UIDropDownMenu_SetText(dropdown, v)
                onSelect(k)
            end
            info.checked = (selected == k)
            UIDropDownMenu_AddButton(info)
        end
    end)

    if options[selected] then UIDropDownMenu_SetText(dropdown, options[selected]) end
    if tooltip then setTooltip(dropdown, label, tooltip) end

    container.dropdown = dropdown
    return container
end

-- 通用控件工厂 - 统一所有控件创建
local ControlFactory = {
    CreateButton = function(parent, text, width, height, onClick, tooltip)
        local btn = WanTinyUI.CreateTabButton(parent, text, width or 80, height or 25)
        if onClick then btn:SetScript("OnClick", onClick) end
        if tooltip then setTooltip(btn, tooltip.title or text, tooltip.desc, "ANCHOR_TOP") end
        return btn
    end,

    CreateSlider = function(parent, label, x, y, min, max, step, initial, onChange, format, tooltip, options)
        return CreateLabeledSlider(parent, label, x, y, min, max, step, initial, onChange, format, tooltip, options)
    end,

    CreateCheckbox = function(parent, label, x, y, checked, onChange, tooltip)
        return CreateLabeledCheckButton(parent, label, x, y, checked, onChange, tooltip)
    end,

    -- 批量创建控件
    CreateGrid = function(parent, items, cols, itemWidth, startY, createFunc)
        local parentWidth = parent:GetWidth() or WanTinyUI.FRAME_WIDTH
        local startX = LayoutUtils.GetCenteredStartX(parentWidth, itemWidth, cols, 0)
        local controls = {}

        for i, item in ipairs(items) do
            local x, y = LayoutUtils.GetGridPosition(i, cols, itemWidth, 35, startX, startY, 0)
            controls[i] = createFunc(parent, item, x, y)
        end
        return controls
    end,

    -- 创建标题
    CreateTitle = function(parent, text, y, color)
        local title = parent:CreateFontString(nil, "OVERLAY")
        title:SetPoint("TOP", parent, "TOP", 0, y or -15)
        setFont(title, "LARGE")
        title:SetText(text)
        title:SetTextColor(unpack(color or COLORS.TITLE))
        return title
    end
}

-- 将控件工厂暴露给WanTinyUI
WanTinyUI.ControlFactory = ControlFactory

-- ==================== 颜色工具 ====================

-- 颜色工具函数
function WanTinyUI.RGB(hex, Alpha)
    local r, g, b = tonumber(hex:sub(1,2), 16)/255, tonumber(hex:sub(3,4), 16)/255, tonumber(hex:sub(5,6), 16)/255
    return Alpha and {r, g, b, Alpha} or {r, g, b}
end

function WanTinyUI.GetClassRGB()
    local _, class = UnitClass("player")
    return class and {GetClassColor(class)} or {1, 1, 1}
end

-- ==================== 主框架创建 ====================


function WanTinyUI.CreateMainFrame(name, titleText)
    local r, g, b = unpack(WanTinyUI.GetClassRGB())
    
    local f = CreateFrame("Frame", name, UIParent, "BackdropTemplate")
    f:SetBackdrop({edgeFile = "Interface/ChatFrame/ChatFrameBackground", edgeSize = 1})
    f:SetBackdropBorderColor(r, g, b, WanTinyUI.borderAlpha)
    f:SetSize(WanTinyUI.FRAME_WIDTH, WanTinyUI.FRAME_HEIGHT)
    f:SetPoint("CENTER")
    f:SetMovable(true)
    f:SetToplevel(true)
    f:SetClampedToScreen(true)
    f:SetScript("OnMouseUp", function(self) self:StopMovingOrSizing() end)
    f:SetScript("OnMouseDown", function(self) self:StartMoving() end)

    local l = f:CreateLine()
    l:SetColorTexture(r, g, b, WanTinyUI.borderAlpha)
    l:SetStartPoint("TOPLEFT", 1, -21)
    l:SetEndPoint("TOPRIGHT", -1, -21)
    l:SetThickness(1)

    f.titleBg = f:CreateTexture(nil, "BACKGROUND", nil, 1)
    f.titleBg:SetPoint("TOPLEFT")
    f.titleBg:SetPoint("BOTTOMRIGHT", f, "TOPRIGHT", 0, -22)
    f.titleBg:SetTexture("Interface\\Buttons\\WHITE8x8")
    f.titleBg:SetGradient("VERTICAL", CreateColor(r, g, b, .2), CreateColor(r, g, b, .0))

    f.Bg = f:CreateTexture(nil, "BACKGROUND", nil, 0)
    f.Bg:SetAllPoints()
    f.Bg:SetColorTexture(.01, .01, .01, .5)

    f.CloseButton = CreateFrame("Button", nil, f, "UIPanelCloseButton")
    f.CloseButton:SetPoint("TOPRIGHT", 5, 5)

    f.titleText = f:CreateFontString()
    f.titleText:SetPoint("TOP", 0, -4)
    setFont(f.titleText, "LARGE")
    f.titleText:SetText(titleText)
    
    return f
end



-- 创建标签页按钮
function WanTinyUI.CreateTabButton(parent, text, width, height)
    local r, g, b = unpack(WanTinyUI.GetClassRGB())

    local bt = CreateFrame("Button", nil, parent, "BackdropTemplate")
    setStandardBackdrop(bt, 0.07)
    
    -- 使用固定宽度，不进行动态调整（避免超出面板）
    local textObj = setButtonText(bt, text, 16, COLORS.GOLD)
    local fixedWidth = width or 70
    bt:SetSize(fixedWidth, height or 28)

    -- 添加阴影效果
    local shadow = bt:CreateTexture(nil, "BACKGROUND")
    shadow:SetPoint("TOPLEFT", bt, "TOPLEFT", 2, -2)
    shadow:SetPoint("BOTTOMRIGHT", bt, "BOTTOMRIGHT", 2, -2)
    shadow:SetColorTexture(0, 0, 0, 0.5)

    function bt:SetSelected(selected)
        self.selected = selected
        local bgAlpha = selected and 0.5 or 0.07
        local borderAlpha = selected and 1 or WanTinyUI.borderAlpha
        self:SetBackdropColor(selected and r or 0, selected and g or 0, selected and b or 0, bgAlpha)
        self:SetBackdropBorderColor(r, g, b, borderAlpha)
    end

    bt:SetScript("OnEnter", function(self)
        local bgAlpha = self.selected and 0.5 or 0.28
        self:SetBackdropColor(r, g, b, bgAlpha)
        self:SetBackdropBorderColor(r, g, b, 1)
    end)
    bt:SetScript("OnLeave", function(self)
        local bgAlpha = self.selected and 0.5 or 0.07
        local borderAlpha = self.selected and 1 or WanTinyUI.borderAlpha
        self:SetBackdropColor(self.selected and r or 0, self.selected and g or 0, self.selected and b or 0, bgAlpha)
        self:SetBackdropBorderColor(r, g, b, borderAlpha)
    end)

    return bt
end



-- 主要功能实现
function WanTinyUI.Init()
    -- 创建主窗口
    local f = WanTinyUI.CreateMainFrame("WanTinyUI_MainFrame", "|cff00bfff<WanTiny>|r|cffff80ff晚妹的小合集|r")
    WanTinyUI.MainFrame = f
    
    -- 左侧标题
    WanTinyUI.TabTitleLeft = f:CreateFontString(nil, "ARTWORK")
    WanTinyUI.TabTitleLeft:SetFont(STANDARD_TEXT_FONT, 20, "OUTLINE")
    WanTinyUI.TabTitleLeft:SetPoint("TOPLEFT", f, "TOPLEFT", 15, -25)
    WanTinyUI.TabTitleLeft:SetTextColor(unpack(WanTinyUI.RGB("00BFFF")))

    -- 右侧版本号
    WanTinyUI.TabTitleRight = f:CreateFontString(nil, "ARTWORK", "GameFontNormal")
    setFont(WanTinyUI.TabTitleRight, "LARGE")
    WanTinyUI.TabTitleRight:SetPoint("TOPRIGHT", f, "TOPRIGHT", -18, -30)
    WanTinyUI.TabTitleRight:SetTextColor(1, 1, 1)
    WanTinyUI.CreateInstructionButton()
    WanTinyUI.CreateMainContent()
    tinsert(UISpecialFrames, "WanTinyUI_MainFrame")
end

function WanTinyUI.CreateInstructionButton()
    local f = CreateFrame("Frame", nil, WanTinyUI.MainFrame)
    f:SetPoint("TOPLEFT", WanTinyUI.MainFrame, "TOPLEFT", 8, -1)
    f:SetHitRectInsets(0, 0, 0, 0)
    local t = f:CreateFontString()
    t:SetPoint("CENTER")
    setFont(t, "LARGE")
    t:SetJustifyH("LEFT")
    t:SetText("说明书")
    t:SetTextColor(unpack(COLORS.GREEN))
    -- 使用GetUnboundedStringWidth动态调整宽度，类似BiaoGeAI的实现
    f:SetSize(t:GetUnboundedStringWidth() + 10, 20)
    WanTinyUI.InstructionButton = f
    
    f:SetScript("OnEnter", function(self)
        GameTooltip:SetOwner(self, "ANCHOR_NONE")
        GameTooltip:SetPoint("TOPLEFT", self, "BOTTOMLEFT")
        GameTooltip:ClearLines()
        GameTooltip:AddLine("|cff00bfff• 使用 /wt 打开主界面|r")
        GameTooltip:AddLine("|cffffd200• 每个模块都可单独启用/禁用，重载界面后生效|r")
        GameTooltip:AddLine("|cff00ff00• 如遇问题可尝试 /reload 重载界面，更多功能陆续添加中|r")
        GameTooltip:Show()
        t:SetTextColor(unpack(COLORS.WHITE))
    end)
    
    f:SetScript("OnLeave", function(self)
        GameTooltip:Hide()
        t:SetTextColor(unpack(COLORS.GREEN))
    end)
end

-- 创建主内容区域（包含所有标签页内容的容器）
function WanTinyUI.CreateMainContent()
    local mainFrame = WanTinyUI.MainFrame
    
    -- 创建内容容器框架
    local contentFrame = CreateFrame("Frame", nil, mainFrame, "BackdropTemplate")
    contentFrame:SetPoint("TOPLEFT", mainFrame, "TOPLEFT", 10, -50)
    contentFrame:SetPoint("BOTTOMRIGHT", mainFrame, "BOTTOMRIGHT", -10, 10)
    contentFrame:SetBackdrop({
        bgFile = "Interface/ChatFrame/ChatFrameBackground",
        edgeFile = "Interface/ChatFrame/ChatFrameBackground",
        edgeSize = 1
    })
    contentFrame:SetBackdropColor(0, 0, 0, 0)
    contentFrame:SetBackdropBorderColor(0.3, 0.3, 0.3, 0.5)
    WanTinyUI.contentFrame = contentFrame
    
    -- 创建所有标签页的内容面板
    WanTinyUI.CreateTabContents()
    -- 创建底部标签页切换按钮
    WanTinyUI.CreateBottomTabs()
end

-- ==================== 标签页内容 ====================

-- 创建所有标签页的内容面板
function WanTinyUI.CreateTabContents()
    local contentFrame = WanTinyUI.contentFrame
    WanTinyUI.tabContents = {}
    
    -- 标签页配置表：定义每个标签页的内容创建方式
    local tabConfigs = {
        -- 标签页1: 模块开关面板 - 包含17个功能模块的启用/禁用开关
        {createFunc = WanTinyUI.CreateSettingsPanel, visible = true},

        -- 标签页2: CVar设置面板 - 游戏参数调整界面
        {createFunc = WanTinyUI.CreateCVarPanel, visible = false},

        -- 标签页3: 快捷标记设置面板
        {createFunc = WanTinyUI.CreateWanMenuPanel, visible = false},

        -- 标签页4: WCombatTimes战斗计时器设置面板
        {createFunc = WanTinyUI.CreateWCombatTimesPanel, visible = false}
    }
    
    -- 为每个标签页创建对应的内容面板
    for i, config in ipairs(tabConfigs) do
        local tab = CreateFrame("Frame", nil, contentFrame)
        tab:SetAllPoints(contentFrame)
        if not config.visible then tab:Hide() end
        WanTinyUI.tabContents[i] = tab
        
        -- 根据配置创建具体内容
        if config.createFunc then
            config.createFunc(tab)  -- 调用专门的创建函数
        elseif config.text then
            -- 创建占位文本
            local text = tab:CreateFontString()
            text:SetPoint("CENTER")
            setFont(text, "LARGE")
            text:SetTextColor(0.7, 0.7, 0.7)
            text:SetText(config.text)
        end
    end
end

-- 【标签页1】模块配置数据：17个功能模块的详细信息
local MODULE_CONFIGS = {
    {"BuffTimers", "|cff00bfff光环计时|r", "显示自身和队友的增益/减益持续时间"},
    {"idTip", "|cffffd200ID提示|r", "鼠标提示中显示NPC/法术/物品ID"},
    {"Focuser", "|cffb8860b快捷焦点|r", "快捷设置焦点目标，Shift+左键设置/取消焦点"},
    {"RaidInfoFrame", "|cffc41f3b团队信息|r", "显示团队副本CD在团队面板"},
    {"RaidCD", "|cffc41f3b团本CD|r", "在未进团时显示所有团本CD锁定状态"},
    {"WanRightClick", "|cff00ff96右键增强|r", "自定义右键菜单，增强社交操作"},
    {"iColor", "|cff69ccf0好友染色|r", "好友面板名字职业染色"},
    {"StatusInfo", "|cffabd473状态信息|r", "显示法强、攻强、护甲等角色状态"},
    {"WanOutOfRange", "|cffff0000技能超距|r", "目标超出技能/交互距离后技能变为红色"},
    {"QuestPrice", "|cffff8000任务价值|r", "任务奖励物品显示商店售价"},
    {"AutoProfiler", "|cffaaaaaa关闭分析|r", "不确定是否真实有效,自我安慰"},
    {"WCvar", "|cff80c0ffCVar设置|r", "批量设置常用CVar参数，优化游戏体验"},
    {"MHQuickButton", "|cffff7d0a集结号按钮|r", "为MeetingHorn添加一键喊话/活动快捷按钮，可自定义喊话内容。"},
    {"BannerHider", "|cffb22222隐藏集结广告|r", "隐藏MeetingHorn(集结号)右侧的广告Banner"},
    {"Buttonstorage", "|cff00ff00按钮收纳|r", "收纳小地图周围的插件按钮，提供拖拽、点击展开等功能"},
    {"WanMenu", "|cffff80ff快捷标记|r", "提供团队标记、倒计时等快捷功能，支持自定义大小和位置"},
    {"WCombatTimes", "|cffff6b6b战斗计时|r", "显示战斗持续时间和战斗状态变化提醒，支持自定义横幅和声音"},
    {"WChat", "|cff00ff00聊天增强|r", "增强聊天功能，包含聊天条、表情输入、密语记录、聊天复制等功能"}
}

-- 【标签页1】创建模块开关设置面板
function WanTinyUI.CreateSettingsPanel(parent)
    _G.WanTinyDB = _G.WanTinyDB or {config = {enabledModules = {}}}
    _G.WanTinyDB.Map = _G.WanTinyDB.Map or {MiniButShouNa_YN = 1, MinimapPointMode = 1, MiniButPerRow = 6, MiniButHideDelay = 1.5}
    local enabledModules = _G.WanTinyDB.config.enabledModules

    -- 模块开关标题
    local currentY = -10
    ControlFactory.CreateTitle(parent, "|cff00ff00模块开关设置|r", currentY)
    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING

    -- 创建模块复选框
    ControlFactory.CreateGrid(parent, MODULE_CONFIGS, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, config, x, y)
        local moduleName, displayName, description = config[1], config[2], config[3]
        local checkBox = ControlFactory.CreateCheckbox(parent, displayName, x, y,
            enabledModules[moduleName] ~= false,
            function(self) enabledModules[moduleName] = self:GetChecked() end, description)
        return checkBox
    end)

    local reloadButton = ControlFactory.CreateButton(parent, "重载生效", WanTinyUI.LAYOUT.BUTTON_WIDTH, 25, ReloadUI)
    reloadButton:SetPoint("BOTTOMRIGHT", parent, "BOTTOMRIGHT", -10, 10)

    -- 小地图按钮收纳设置（增加与上方复选框的间距）
    currentY = currentY - math.ceil(#MODULE_CONFIGS / 4) * 35  -- 基于模块数量计算复选框占用的空间
    currentY = currentY - WanTinyUI.LAYOUT.SECTION_GAP  -- 增加额外间距，避免与复选框太紧
    ControlFactory.CreateTitle(parent, "|cff00ff00小地图按钮收纳设置|r", currentY)
    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING

    if not checkModuleLoaded("Buttonstorage") then
        createModuleNotLoadedText(parent, "Buttonstorage", "TOP", parent, 0, currentY)
        return
    end

    local function updateMinimap()
        C_Timer.After(0.1, function()
            if _G.WanTiny_RestoreAllCollectedButtons then _G.WanTiny_RestoreAllCollectedButtons() end
            if WanTinyDB.Map.MiniButShouNa_YN == 1 and _G.WanTiny_ShowAllCollectedButtons then _G.WanTiny_ShowAllCollectedButtons() end
        end)
    end

    local controlWidth, spacing = WanTinyUI.LAYOUT.SLIDER_WIDTH, WanTinyUI.LAYOUT.EXTRA_SPACING
    local startX = LayoutUtils.GetCenteredStartXSafe(parent, controlWidth, 2, spacing)

    -- 第1行：复选框和下拉框
    local checkBox = CreateLabeledCheckButton(parent, "启用收纳", startX, currentY,
        WanTinyDB.Map.MiniButShouNa_YN==1,
        function(self) WanTinyDB.Map.MiniButShouNa_YN = self:GetChecked() and 1 or 2; updateMinimap() end)

    local dropDown = CreateLabeledDropDown(parent, "按钮位置：", startX+controlWidth+spacing, currentY,
        {[1]="小地图",[2]="聊天框",[3]="自由"}, WanTinyDB.Map.MinimapPointMode,
        function(k) WanTinyDB.Map.MinimapPointMode=k; if _G.WanTiny_MiniMapBut and _G.WanTiny_MiniMapBut.SetButtonStyle then _G.WanTiny_MiniMapBut:SetButtonStyle(k) end end)
    currentY = currentY - 35  -- 第1行完成

    -- 第2行：两个滑块
    local slider1 = CreateLabeledSlider(parent, "每行按钮：", startX, currentY, 3, 12, 1,
        WanTinyDB.Map.MiniButPerRow or 6,
        function(self,v) WanTinyDB.Map.MiniButPerRow=math.floor(v+0.5); updateMinimap() end,
        function(v) return tostring(math.floor(v+0.5)) end)

    local slider2 = CreateLabeledSlider(parent, "自动隐藏：", startX+controlWidth-10, currentY, 0.5, 5, 0.1,
        WanTinyDB.Map.MiniButHideDelay or 1.5,
        function(self,v) WanTinyDB.Map.MiniButHideDelay=v end,
        function(v) return string.format("%.1f 秒",v) end)
    currentY = currentY - 35  -- 第2行完成


end

-- CVar按钮回调函数
local function refreshCVarSettings(cvarSettings, sliders, formatValue)
    for _, setting in ipairs(cvarSettings) do
        local slider = sliders[setting.cvar]
        local currentValue = GetCVar(setting.cvar)
        if currentValue then
            local value = tonumber(currentValue) or setting.default
            slider:SetValue(value)
            slider.valueText:SetText(formatValue(value))
        end
    end
end

local function applyCVarSettings(sliders)
    for cvarName, slider in pairs(sliders) do
        pcall(SetCVar, cvarName, slider:GetValue())
    end
end

local function resetCVarSettings(cvarSettings, sliders, formatValue)
    for _, setting in ipairs(cvarSettings) do
        local slider = sliders[setting.cvar]
        slider:SetValue(setting.default)
        slider.valueText:SetText(formatValue(setting.default))
    end
end

-- 【标签页2】创建CVar设置面板
function WanTinyUI.CreateCVarPanel(parent)
    -- 检查模块加载状态，分别处理两个模块
    local wcvarLoaded = checkModuleLoaded("WCvar")
    local wanmenuLoaded = checkModuleLoaded("WanMenu")

    local currentY = -10

    -- WCvar CVar设置
    ControlFactory.CreateTitle(parent, "|cff80c0ffCVar设置|r", currentY)
    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING

    if wcvarLoaded then

    local cvarSettings = WanTiny_CVarSettings
    local formatValue = function(val) return type(val) == "number" and string.format("%.2f", val):gsub("%.?0+$", "") or tostring(val) end

    -- 创建CVar设置（每行2个设置，每个设置为"标签 滑块"水平布局）
    local sliders = {}
    local cols = 2  -- 每行2个设置
    local ROW_HEIGHT = 35  -- 标准行高
    local parentWidth = LayoutUtils.GetParentWidth(parent)
    local itemWidth = (parentWidth - 40) / cols  -- 每个设置的宽度（减去边距）
    local startX = WanTinyUI.LAYOUT.LEFT_MARGIN

    -- 按行处理CVar设置
    local totalRows = math.ceil(#cvarSettings / cols)

    for row = 1, totalRows do
        for col = 1, cols do
            local index = (row - 1) * cols + col
            if index <= #cvarSettings then
                local setting = cvarSettings[index]
                local x = startX + (col - 1) * itemWidth
                local y = currentY

                -- 创建水平布局的滑块（标签在左，滑块在右）
                local currentValue = GetCVar(setting.cvar)
                local initialValue = currentValue and tonumber(currentValue) or setting.default
                local slider = ControlFactory.CreateSlider(parent, setting.name, x, y, setting.min, setting.max, setting.step,
                    initialValue, nil, formatValue, setting.description, { verticalLayout = false, labelColor = setting.nameColor or COLORS.WHITE })
                sliders[setting.cvar] = slider
            end
        end
        currentY = currentY - ROW_HEIGHT  -- 移动到下一行
    end

    -- 添加CVar设置和按钮之间的间距
    currentY = currentY - WanTinyUI.LAYOUT.EXTRA_SPACING

    -- CVar操作按钮（独立行）
    local buttonWidth, buttonSpacing = WanTinyUI.LAYOUT.BUTTON_WIDTH, WanTinyUI.LAYOUT.CVAR_BUTTON_SPACING
    local buttonStartX = LayoutUtils.GetCenteredStartXSafe(parent, buttonWidth, 3, buttonSpacing)

    local buttons = {
        {"刷新", function() refreshCVarSettings(cvarSettings, sliders, formatValue) end, "重新读取当前CVar实际值"},
        {"应用", function() applyCVarSettings(sliders) end, "应用所有设置到游戏"},
        {"重置", function() resetCVarSettings(cvarSettings, sliders, formatValue) end, "恢复为默认值"}
    }

    for i, btn in ipairs(buttons) do
        local button = ControlFactory.CreateButton(parent, btn[1], buttonWidth, 25, btn[2], {desc = btn[3]})
        button:SetPoint("TOPLEFT", parent, "TOPLEFT", buttonStartX + (i-1) * (buttonWidth + buttonSpacing), currentY)
    end
    currentY = currentY - 35  -- 按钮占用1行



    else
        -- WCvar模块未加载时显示提示
        createModuleNotLoadedText(parent, "WCvar", "TOP", parent, 0, currentY)
        currentY = currentY - 120  -- 为提示文字预留足够空间
    end

    -- 添加间距
    currentY = currentY - WanTinyUI.LAYOUT.SECTION_GAP

    -- WanMenu 快捷标记设置
    ControlFactory.CreateTitle(parent, "|cffff80ff快捷标记设置|r", currentY)
    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING

    if wanmenuLoaded then

        local function GetWanMenuConfig()
            if _G.WanMenu and _G.WanMenu.GetConfig then return _G.WanMenu.GetConfig() end
            _G.WanMenuDB = _G.WanMenuDB or {config = {}}
            return _G.WanMenuDB.config
        end

        local function UpdateWanMenuConfig(key, value)
            local config = GetWanMenuConfig()
            config[key] = value
            if _G.WanMenu and _G.WanMenu.UpdateFromConfig then _G.WanMenu.UpdateFromConfig(key, value) end
        end

        -- 标记控件
        local markControls = {
            {type="checkbox", key="wanmarkTextBtn", label="|cff00ff00文字按钮|r", desc="显示标记按钮的文字标签"},
            {type="checkbox", key="wanmarkIconBtn", label="|cffff8000图标按钮|r", desc="显示标记按钮的图标"},
            {type="checkbox", key="wanmarkFollow", label="|cff00bfff锁定|r", desc="锁定标记框架位置"},
            {type="slider", key="defaultCountdown", label="|cffabd473倒数：|r", min=5, max=90, step=1, default=10, format=function(v) return tostring(math.floor(v)).."秒" end, desc="开团倒计时默认秒数"},
            {type="slider", key="markScale", label="|cffabd473大小：|r", min=0.5, max=2.0, step=0.1, default=1.0, format=function(v) return string.format("%.1f", v) end, desc="调整标记栏的整体大小（0.5-2.0倍）"}
        }

        local cfg = GetWanMenuConfig()
        local checkboxStartX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, 3, 0)

        -- 创建复选框（独立行）
        for i, ctrl in ipairs(markControls) do
            if ctrl.type == "checkbox" then
                local x = checkboxStartX + (i-1) * WanTinyUI.LAYOUT.CHECKBOX_WIDTH
                local value = cfg[ctrl.key] ~= false
                CreateLabeledCheckButton(parent, ctrl.label, x, currentY, value, function(self) UpdateWanMenuConfig(ctrl.key, self:GetChecked()) end, ctrl.desc)
            end
        end
        currentY = currentY - 35  -- 复选框占用1行

        -- 创建滑块（独立行，两个滑块在同一行）
        local sliderWidth = WanTinyUI.LAYOUT.SLIDER_WIDTH
        local sliderSpacing = 30
        local sliderStartX = LayoutUtils.GetCenteredStartXSafe(parent, sliderWidth * 2 + sliderSpacing, 1, 0)
        local sliderIndex = 0

        for i, ctrl in ipairs(markControls) do
            if ctrl.type == "slider" then
                local x = sliderStartX + sliderIndex * (sliderWidth + sliderSpacing)
                CreateLabeledSlider(parent, ctrl.label, x, currentY, ctrl.min, ctrl.max, ctrl.step, cfg[ctrl.key] or ctrl.default,
                    function(self, value) UpdateWanMenuConfig(ctrl.key, value) end, ctrl.format, ctrl.desc)
                sliderIndex = sliderIndex + 1
            end
        end
        currentY = currentY - 35  -- 滑块占用1行
    else
        -- WanMenu模块未加载时显示提示
        createModuleNotLoadedText(parent, "WanMenu", "TOP", parent, 0, currentY)
    end
end

-- 【标签页3】快捷标记面板（简化版）
function WanTinyUI.CreateWanMenuPanel(parent)
    if not checkModuleLoaded("WanMenu") then
        createModuleNotLoadedText(parent, "WanMenu")
        return
    end
    
    -- 简单的说明文字
    local desc = parent:CreateFontString(nil, "ARTWORK", "GameFontNormal")
    desc:SetPoint("TOPLEFT", parent, "TOPLEFT", 20, -20)
    desc:SetJustifyH("LEFT")
    desc:SetText("|cffff80ff快捷标记功能设置|r\n\n快捷标记的详细设置已移至 |cff00ff00CVAR设置|r 标签页下方。\n\n包含团队标记按钮、倒计时功能、显示选项等设置。\n\n当前标签页保留用于未来扩展功能。")
    desc:SetWidth(500)
    desc:SetNonSpaceWrap(true)
end

-- 【标签页4】创建WCombatTimes战斗计时器设置面板
function WanTinyUI.CreateWCombatTimesPanel(parent)
    if parent.wcombatPanelCreated then return end



    parent.wcombatPanelCreated = true

    -- 检查模块加载状态，分别处理两个模块
    local wcombatLoaded = checkModuleLoaded("WCombatTimes")
    local wchatLoaded = checkModuleLoaded("WChat")

    local ROW_HEIGHT = 35  -- 标准行高35px
    local currentY = -10   -- 起始坐标

    -- WCombatTimes 战斗计时器设置
    ControlFactory.CreateTitle(parent, "|cffff6b6b战斗计时器设置|r", currentY)
    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING

    if wcombatLoaded then
        local config = _G.WCT.Config
        local SetConfig = function(k,v) _G.WCT.SetConfig(k,v) end

    local function CreateCheck(text, x, y, key, tip, callback)
        return ControlFactory.CreateCheckbox(parent, text, x, y, config[key] ~= false,
            callback or function(self) SetConfig(key, self:GetChecked()) end, tip)
    end

    local function CreateEditBox(key, x, y, tip, options)
        options = options or {}
        local hasLabel = options.label ~= nil
        local containerHeight = hasLabel and ROW_HEIGHT * 2 or 20

        local container = CreateFrame("Frame", nil, parent)
        container:SetSize(options.width or 120, containerHeight)
        container:SetPoint("TOPLEFT", parent, "TOPLEFT", x, y - (hasLabel and 0 or 7))

        local editBox
        if hasLabel then
            -- 创建标签
            local lbl = container:CreateFontString(nil, "OVERLAY", "GameFontNormal")
            lbl:SetPoint("TOP", container, "TOP", 0, -2)
            lbl:SetText(options.label)
            setFont(lbl, "LARGE")
            lbl:SetTextColor(unpack(COLORS.WHITE))

            -- 创建输入框
            editBox = CreateFrame("EditBox", nil, container)
            editBox:SetSize(120, 20)
            editBox:SetPoint("TOP", lbl, "BOTTOM", 0, -8)
        else
            editBox = CreateFrame("EditBox", nil, container)
            editBox:SetSize(120, 20)
            editBox:SetPoint("TOPLEFT", container, "TOPLEFT", 0, 0)
        end

        editBox:SetAutoFocus(false)
        editBox:SetMaxLetters(50)
        editBox:SetFont(STANDARD_TEXT_FONT, 12, "OUTLINE")
        editBox:SetTextColor(1, 1, 1)
        editBox:SetTextInsets(5, 5, 0, 0)

        -- 创建背景和边框
        local bg = editBox:CreateTexture(nil, "BACKGROUND")
        bg:SetAllPoints(editBox)
        bg:SetColorTexture(0, 0, 0, 0.5)

        local border = editBox:CreateTexture(nil, "BORDER")
        border:SetAllPoints(editBox)
        border:SetColorTexture(0.5, 0.5, 0.5, 0.8)

        -- 设置初始值和事件
        editBox:SetText(config[key] or "")
        editBox:SetScript("OnTextChanged", function(self)
            local val = self:GetText()
            SetConfig(key, val)
        end)

        editBox:SetScript("OnEditFocusGained", function(self)
            border:SetColorTexture(1, 1, 0, 0.8)
        end)
        editBox:SetScript("OnEditFocusLost", function(self)
            border:SetColorTexture(0.5, 0.5, 0.5, 0.8)
        end)
        editBox:SetScript("OnEnterPressed", function(self) self:ClearFocus() end)
        editBox:SetScript("OnEscapePressed", function(self) self:ClearFocus() end)
        editBox:EnableMouse(true)

        if tip then setTooltip(editBox, tip, tip) end
        return hasLabel and container or editBox
    end

    -- 创建复选框
    local checkConfigs = {
        {"|cff00ff00显示计时器|r", "showMainFrame", "启用或禁用主计时器框体", function(self)
            local checked = self:GetChecked()
            SetConfig("showMainFrame", checked)
            local frame = LayoutUtils.SafeGetGlobal("WCombatTimesFrame")
            if frame and frame.Show and frame.Hide then
                frame[checked and "Show" or "Hide"](frame)
            end
        end},
        {"|cffff8000显示横幅|r", "showBanner", "进入/离开战斗时显示横幅提醒"},
        {"|cff00bfff进战音效|r", "playSoundOnEnter", "进入战斗时播放声音提醒"},
        {"|cff00bfff脱战音效|r", "playSoundOnLeave", "离开战斗时播放声音提醒"}
    }

    local checkBoxes = ControlFactory.CreateGrid(parent, checkConfigs, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, cfg, x, y)
        return CreateCheck(cfg[1], x, y, cfg[2], cfg[3], cfg[4])
    end)
    currentY = currentY - ROW_HEIGHT  -- 复选框占用1行

    -- 创建输入框
    local editConfigs = {
        {"进战主文字", "enterBannerTitle"},
        {"进战次文字", "enterBannerLabel"},
        {"脱战主文字", "leaveBannerTitle"}
    }

    -- 创建输入框和按钮的混合布局（4列：3个输入框 + 1个按钮列）
    local parentWidth = LayoutUtils.GetParentWidth(parent)
    local itemWidth = WanTinyUI.LAYOUT.ITEM_WIDTH_4COL
    local startX = LayoutUtils.GetCenteredStartX(parentWidth, itemWidth, 4, 0)

    -- 第3行：创建标签行（每个标签占35px高度）
    for i, cfg in ipairs(editConfigs) do
        local labelName = "WCombatLabel_" .. cfg[2]
        if not _G[labelName] then
            local x, y = LayoutUtils.GetGridPosition(i, 4, itemWidth, ROW_HEIGHT, startX, currentY, 0)
            local lbl = parent:CreateFontString(labelName, "OVERLAY", "GameFontNormal")
            lbl:SetPoint("TOPLEFT", parent, "TOPLEFT", x, y - 12)
            lbl:SetText(cfg[1])
            lbl:SetTextColor(unpack(COLORS.WHITE))
            _G[labelName] = lbl
        end
    end

    -- 创建测试横幅按钮（第4列，第3行）
    local testBannerBtn = ControlFactory.CreateButton(parent, "测试横幅", WanTinyUI.LAYOUT.BUTTON_WIDTH, 25, function()
        local U1CT = LayoutUtils.SafeGetGlobal("WCombatTimesFrame")
        if U1CT then
            -- 测试横幅
            if U1CT.PlayBanner and type(U1CT.PlayBanner) == "function" then
                U1CT.PlayBanner(true)
                C_Timer.After(2, function()
                    if U1CT and U1CT.PlayBanner and type(U1CT.PlayBanner) == "function" then
                        U1CT.PlayBanner(false)
                    end
                end)
            end

            -- 测试音效
            if U1CT.PlaySound and type(U1CT.PlaySound) == "function" then
                U1CT.PlaySound(true)
                C_Timer.After(2.5, function()
                    if U1CT and U1CT.PlaySound and type(U1CT.PlaySound) == "function" then
                        U1CT.PlaySound(false)
                    end
                end)
            end
        end
    end, {desc="测试战斗横幅和音效"})
    local testX, testY = LayoutUtils.GetGridPosition(4, 4, itemWidth, ROW_HEIGHT, startX, currentY, 0)
    testBannerBtn:SetPoint("TOPLEFT", parent, "TOPLEFT", testX, testY - 5)  -- 垂直居中对齐

    currentY = currentY - ROW_HEIGHT  -- 第3行完成

    -- 第4行：创建输入框行（每个输入框占35px高度）
    local editBoxes = {}
    for i, cfg in ipairs(editConfigs) do
        local x, y = LayoutUtils.GetGridPosition(i, 4, itemWidth, ROW_HEIGHT, startX, currentY, 0)
        editBoxes[cfg[2]] = CreateEditBox(cfg[2], x, y)
    end

    -- 暂时保存重置按钮的位置，稍后创建
    local resetBtnX, resetBtnY = testX, currentY - 5

    currentY = currentY - ROW_HEIGHT  -- 第4行完成

    -- 创建滑块
    local sliderConfigs = {
        {"|cffabd473横幅Y轴设置|r", -500, 500, 10, (config.bannerPosition and config.bannerPosition[5]) or 200,
         function(self, value)
             local pos = config.bannerPosition or {"CENTER", "UIParent", "CENTER", 0, 200}
             pos[5] = value
             SetConfig("bannerPosition", pos)
         end, function(v) return tostring(math.floor(v)) end, "调整战斗横幅的垂直位置"},
        {"|cffabd473横幅时间|r", 0.5, 3.0, 0.1, config.bannerDuration or 1.0,
         function(self, value) SetConfig("bannerDuration", value) end,
         function(v) return string.format("%.1f秒", v) end, "横幅提示文字的停留时间"}
    }

    -- 使用与WChat相同的网格布局方式，确保行距一致
    local sliders = {}
    for i, cfg in ipairs(sliderConfigs) do
        local x, y = LayoutUtils.GetGridPosition(i, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT,
            LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.SLIDER_WIDTH, 2, 0), currentY, 0)
        sliders[i] = CreateLabeledSlider(parent, cfg[1], x, y, cfg[2], cfg[3], cfg[4], cfg[5], cfg[6], cfg[7], cfg[8])
    end

    -- 简化的恢复默认按钮
    local resetBtn = ControlFactory.CreateButton(parent, "恢复默认", WanTinyUI.LAYOUT.BUTTON_WIDTH, 25, function()
        if not _G.WCT then return end
        
        -- 调用模块的重置函数
        _G.WCT.ResetSettings()

        -- 重新创建面板以刷新所有UI控件（统一的简单处理方式）
        parent.wcombatPanelCreated = false
        for i = 1, parent:GetNumChildren() do
            local child = select(i, parent:GetChildren())
            if child then child:Hide() end
        end
        WanTinyUI.CreateWCombatTimesPanel(parent)
    end, {desc="将所有设置恢复为默认值"})
    resetBtn:SetPoint("TOPLEFT", parent, "TOPLEFT", resetBtnX, resetBtnY)

        -- 滑块占用1行，更新Y坐标
        currentY = currentY - ROW_HEIGHT
    else
        -- WCombatTimes模块未加载时显示提示
        createModuleNotLoadedText(parent, "WCombatTimes", "TOP", parent, 0, currentY)
        currentY = currentY - 120  -- 为提示文字预留足够空间
    end

    -- 添加战斗计时器设置和聊天增强设置之间的间距
    currentY = currentY - WanTinyUI.LAYOUT.SECTION_GAP

    -- WChat 聊天增强设置
    ControlFactory.CreateTitle(parent, "|cff00ff00WChat 聊天增强设置|r", currentY)
    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING

    if wchatLoaded then

    local wchatConfig = _G.WChatClassicDB
    if not wchatConfig then return end

    -- 保存UI控件引用，用于恢复默认时更新
    local uiControls = {}

    -- 创建WChat复选框
    local wchatCheckConfigs = {
        {"|cff00ff00启用上方输入|r", "UseTopInput", "在聊天框上方显示输入框"},
        {"|cffff8000垂直聊天条|r", "UseVertical", "将聊天按钮垂直排列", function() if _G.WChatUpdateSliderVisibility then C_Timer.After(0.1, _G.WChatUpdateSliderVisibility) end end},
        {"|cffffb347启用频道缩写|r", "EnableChannelShortNames", "将频道名称显示为缩写形式"},
        {"|cff8080ff锁定聊天条|r", "LockChatBar", "解锁后可移动聊天条位置"}
    }

    ControlFactory.CreateGrid(parent, wchatCheckConfigs, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, cfg, x, y)
        local checkbox = ControlFactory.CreateCheckbox(parent, cfg[1], x, y, wchatConfig[cfg[2]], function(self)
            wchatConfig[cfg[2]] = self:GetChecked()
            if _G.WChat and _G.WChat.OnConfigChanged and _G.WChat.OnConfigChanged[cfg[2]] then
                _G.WChat.OnConfigChanged[cfg[2]](wchatConfig[cfg[2]])
            end
            if cfg[4] then cfg[4]() end
        end, cfg[3])
        uiControls[cfg[2] .. "Check"] = checkbox
        return checkbox
    end)
    currentY = currentY - ROW_HEIGHT  -- 复选框占用1行

    -- 第二行：新增的聊天增强设置
    local wchatEnhanceConfigs = {
        {"|cff00ff00启用时间戳|r", "EnableTimestamp", "在聊天消息前显示时间戳"},
        {"|cffff1493时间颜色设置|r", "TimestampColorButton", "点击设置时间戳颜色"},
        {"|cff87ceeb时间显示格式|r", "TimestampFormatButton", "点击设置时间戳格式"}
    }

    -- 创建第二行的设置控件（与第一行的前3个复选框垂直对齐）
    for i, cfg in ipairs(wchatEnhanceConfigs) do
        -- 使用与第一行相同的起始位置和间距，确保垂直对齐
        local x, y = LayoutUtils.GetGridPosition(i, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, ROW_HEIGHT,
            LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, 4, 0), currentY, 0)

        if cfg[2] == "TimestampColorButton" then
            -- 创建容器以确保与复选框和下拉菜单垂直对齐
            local container = CreateFrame("Frame", nil, parent)
            container:SetSize(WanTinyUI.LAYOUT.CHECKBOX_WIDTH, WanTinyUI.LAYOUT.CHECKBOX_HEIGHT)
            container:SetPoint("TOPLEFT", parent, "TOPLEFT", x, y)

            -- 使用通用按钮创建，放在容器中央
            local colorBtn = ControlFactory.CreateButton(container, "时间颜色", 80, 25, function()
                WChat:ShowTimestampColorPicker()
            end, {title = cfg[1], desc = cfg[3]})
            colorBtn:SetPoint("CENTER", container, "CENTER", 0, 0)
            uiControls.timestampColorBtn = colorBtn
        elseif cfg[2] == "TimestampFormatButton" then
            -- 使用通用下拉菜单创建
            local formatOptions = {
                "[%H:%M:%S] - [06:38:35]",
                "%H:%M:%S - 06:38:35",
                "[%H:%M] - [06:38]",
                "%H:%M - 06:38",
                "[%I:%M:%S %p] - [06:38:35 AM]",
                "%I:%M:%S %p - 06:38:35 AM"
            }

            -- 获取当前格式对应的选项索引
            local currentFormat = wchatConfig.TimestampFormat or "[%H:%M:%S]"
            local formatMap = {
                ["[%H:%M:%S]"] = 1,
                ["%H:%M:%S"] = 2,
                ["[%H:%M]"] = 3,
                ["%H:%M"] = 4,
                ["[%I:%M:%S %p]"] = 5,
                ["%I:%M:%S %p"] = 6
            }
            local currentIndex = formatMap[currentFormat] or 1

            local formatDropdown = CreateLabeledDropDown(parent, cfg[1], x, y, formatOptions, currentIndex, function(index)
                local formatValues = {
                    "[%H:%M:%S]",
                    "%H:%M:%S",
                    "[%H:%M]",
                    "%H:%M",
                    "[%I:%M:%S %p]",
                    "%I:%M:%S %p"
                }
                wchatConfig.TimestampFormat = formatValues[index]
                if _G.WChat and _G.WChat.OnConfigChanged and _G.WChat.OnConfigChanged.TimestampFormat then
                    _G.WChat.OnConfigChanged.TimestampFormat(wchatConfig.TimestampFormat)
                end
            end, cfg[3])
            uiControls.timestampFormatDropdown = formatDropdown
        else
            -- 创建复选框
            local checkbox = ControlFactory.CreateCheckbox(parent, cfg[1], x, y, wchatConfig[cfg[2]], function(self)
                wchatConfig[cfg[2]] = self:GetChecked()
                if _G.WChat and _G.WChat.OnConfigChanged and _G.WChat.OnConfigChanged[cfg[2]] then
                    _G.WChat.OnConfigChanged[cfg[2]](wchatConfig[cfg[2]])
                end
            end, cfg[3])
            uiControls[cfg[2] .. "Check"] = checkbox
        end
    end
    currentY = currentY - ROW_HEIGHT  -- 第二行占用1行

    -- 创建动态间距滑块（根据垂直聊天条状态切换）
    local distanceSlider
    local distanceSliderContainer
    local function updateDistanceSlider()
        if not distanceSlider or not distanceSliderContainer then return end
        local isVertical = wchatConfig.UseVertical
        local label = isVertical and "|cff00ff00垂直间距|r" or "|cff00ff00水平间距|r"
        local key = isVertical and "DistanceVertical" or "DistanceHorizontal"
        local tooltip = isVertical and "调整垂直排列时按钮之间的间距" or "调整水平排列时按钮之间的间距"
        local cleanLabel = label:gsub("|c%x%x%x%x%x%x%x%x", ""):gsub("|r", "")

        -- 获取当前实际配置值，如果不存在则使用默认值
        local currentValue = wchatConfig[key]
        if currentValue == nil then
            currentValue = 24  -- 统一使用24作为默认值，与WChat.DefaultConfig一致
            wchatConfig[key] = currentValue  -- 设置默认值到配置中
        end

        -- 更新滑块标签
        if distanceSlider.labelText then
            distanceSlider.labelText:SetText(label)
        end

        -- 更新滑块值和当前配置键（临时禁用回调避免循环）
        distanceSlider.currentKey = key
        local originalCallback = distanceSlider:GetScript("OnValueChanged")
        distanceSlider:SetScript("OnValueChanged", nil)
        distanceSlider:SetValue(currentValue)
        distanceSlider:SetScript("OnValueChanged", originalCallback)
        if distanceSlider.valueText then
            distanceSlider.valueText:SetText(tostring(currentValue))
        end

        -- 重新设置提示信息
        setTooltip(distanceSliderContainer, cleanLabel, tooltip)
    end

    -- 创建WChat滑块配置（移除重复的间距滑块）
    local wchatSliderConfigs = {
        {"|cff00ffff按钮透明|r", "AlphaOnLeave", 0.1, 1.0, 0.1, 1.0, function(v) return string.format("%.1f", v) end, "聊天条的默认透明度"},
        {"|cffff8000按钮大小|r", "ButtonSize", 16, 32, 1, 22, nil, "聊天条按钮的大小"},
        {"|cffff69b4预览表情|r", "EmoteIconListSize", 20, 40, 1, 30, nil, "表情选择面板中图标的预览大小"}
    }

    -- 统一的2列滑块布局起始X坐标
    local sliderStartX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.SLIDER_WIDTH, 2, 0)

    -- 创建动态间距滑块（第一行左侧）
    local x, y = LayoutUtils.GetGridPosition(1, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT, sliderStartX, currentY, 0)

    -- 获取初始配置值
    local initialKey = wchatConfig.UseVertical and "DistanceVertical" or "DistanceHorizontal"
    local initialValue = wchatConfig[initialKey] or 24  -- 统一使用24作为默认值
    local initialLabel = wchatConfig.UseVertical and "|cff00ff00垂直间距|r" or "|cff00ff00水平间距|r"
    local initialTooltip = wchatConfig.UseVertical and "调整垂直排列时按钮之间的间距" or "调整水平排列时按钮之间的间距"

    distanceSlider = CreateLabeledSlider(parent, initialLabel, x, y, 10, 50, 1, initialValue,
        function(self, value)
            local key = self.currentKey or "DistanceHorizontal"
            wchatConfig[key] = value
            local wc = LayoutUtils.SafeGetGlobal("WChat")
            if wc and wc.OnConfigChanged and wc.OnConfigChanged[key] then
                wc.OnConfigChanged[key](value)
            end
        end, nil, initialTooltip)

    distanceSlider.currentKey = initialKey
    distanceSliderContainer = distanceSlider:GetParent()  -- 保存容器引用
    uiControls.distanceSlider = distanceSlider

    -- 标签引用已经在CreateLabeledSlider中设置了

    -- WChat配置更新回调（避免重复获取）
    local wchatCallbacks = LayoutUtils.SafeGetGlobal("WChat") and LayoutUtils.SafeGetGlobal("WChat").OnConfigChanged

    -- 第一行右侧：按钮透明滑块
    local cfg1 = wchatSliderConfigs[1]  -- 按钮透明
    local x1, y1 = LayoutUtils.GetGridPosition(2, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT, sliderStartX, currentY, 0)
    local alphaSlider = ControlFactory.CreateSlider(parent, cfg1[1], x1, y1, cfg1[3], cfg1[4], cfg1[5], wchatConfig[cfg1[2]] or cfg1[6],
        function(self, value) LayoutUtils.UpdateConfig(wchatConfig, cfg1[2], value, wchatCallbacks) end, cfg1[7], cfg1[8])
    uiControls.alphaLeaveSlider = alphaSlider

    -- 第二行：按钮大小滑块（左侧）+ 预览表情滑块（右侧）
    local cfg2 = wchatSliderConfigs[2]  -- 按钮大小
    local x2, y2 = LayoutUtils.GetGridPosition(1, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT, sliderStartX, currentY - ROW_HEIGHT, 0)
    local buttonSizeSlider = ControlFactory.CreateSlider(parent, cfg2[1], x2, y2, cfg2[3], cfg2[4], cfg2[5], wchatConfig[cfg2[2]] or cfg2[6],
        function(self, value) LayoutUtils.UpdateConfig(wchatConfig, cfg2[2], value, wchatCallbacks) end, cfg2[7], cfg2[8])
    uiControls.buttonSizeSlider = buttonSizeSlider

    -- 第二行右侧：预览表情滑块
    local x3, y3 = LayoutUtils.GetGridPosition(2, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT, sliderStartX, currentY - ROW_HEIGHT, 0)
    local emoteSlider = ControlFactory.CreateSlider(parent, "|cffff69b4预览表情|r", x3, y3, 20, 40, 1, wchatConfig.EmoteIconListSize or 30,
        function(self, value) LayoutUtils.UpdateConfig(wchatConfig, "EmoteIconListSize", value, wchatCallbacks) end, nil, "表情选择面板中图标的预览大小")
    uiControls.emoteSlider = emoteSlider

    currentY = currentY - ROW_HEIGHT * 2  -- 2行完成

    -- 设置全局更新函数供复选框调用
    _G.WChatUpdateSliderVisibility = updateDistanceSlider
    updateDistanceSlider()  -- 初始化

    -- 简化的WChat恢复默认按钮
    local resetBtnX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.BUTTON_WIDTH, 1, 0)
    local resetBtn = ControlFactory.CreateButton(parent, "恢复默认", WanTinyUI.LAYOUT.BUTTON_WIDTH, 25, function()
        if not (_G.WChat and _G.WChat.OnConfigChanged and _G.WChat.OnConfigChanged.ResetToDefaults) then return end

        -- 调用模块的重置函数
        _G.WChat.OnConfigChanged.ResetToDefaults()
        
        -- 重新创建整个面板以刷新所有UI控件
        parent.wcombatPanelCreated = false
        for i = 1, parent:GetNumChildren() do
            local child = select(i, parent:GetChildren())
            if child then child:Hide() end
        end
        WanTinyUI.CreateWCombatTimesPanel(parent)
    end, {desc="恢复WChat所有设置为默认值"})
        resetBtn:SetPoint("TOPLEFT", parent, "TOPLEFT", resetBtnX, currentY)
    else
        -- WChat模块未加载时显示提示
        createModuleNotLoadedText(parent, "WChat", "TOP", parent, 0, currentY)
    end
end

-- 创建底部标签页切换按钮
function WanTinyUI.CreateBottomTabs()
    local mainFrame = WanTinyUI.MainFrame
    WanTinyUI.tabButtons = {}
    WanTinyUI.activeTab = 1

    -- 标签页按钮布局
    local frameWidth = WanTinyUI.FRAME_WIDTH
    local leftPadding = WanTinyUI.LAYOUT.TAB_PADDING
    local rightPadding = WanTinyUI.LAYOUT.TAB_PADDING
    local availableWidth = frameWidth - leftPadding - rightPadding
    local spacing = WanTinyUI.LAYOUT.TAB_SPACING

    -- 标签页按钮文字定义
    local tabTexts = {"模块开关", "CVAR/标记", "占位", "战斗计时/聊天"}

    -- 计算每个按钮的宽度（平均分配可用宽度）
    local totalSpacing = spacing * (table.getn(tabTexts) - 1)
    local buttonWidth = math.floor((availableWidth - totalSpacing) / table.getn(tabTexts))

    local currentX = leftPadding
    local startY = WanTinyUI.LAYOUT.TAB_BOTTOM_OFFSET

    for i, tabText in ipairs(tabTexts) do
        local bt = WanTinyUI.CreateTabButton(mainFrame, tabText, buttonWidth, 28)

        -- 直接定位到底部
        bt:SetPoint("BOTTOMLEFT", mainFrame, "BOTTOMLEFT", currentX, startY)
        currentX = currentX + buttonWidth + spacing

        bt:SetScript("OnClick", function() WanTinyUI.SelectTab(i) end)
        WanTinyUI.tabButtons[i] = bt
    end

    -- 默认选中第一个标签页
    WanTinyUI.SelectTab(1)
end
local TAB_TITLES = {
    left = {"WanTiny|cffff80ff晚妹的小合集|r", "CVar/快捷标记", "|cffff80ff占位|r", "|cffff6b6b战斗计时|r/|cff00ff00聊天增强|r"},
    right = {"|cff00bfffV 2.0.0|r", "|cff00bfffV 2.0.0|r", "|cff00bfffV 2.0.0|r", "|cff00bfffV 2.0.0|r"}
}

-- 标签页切换核心函数：控制标签页按钮状态和内容显示
function WanTinyUI.SelectTab(tabIndex)
    WanTinyUI.activeTab = tabIndex
    
    -- 更新标签页按钮的选中状态
    if WanTinyUI.tabButtons and type(WanTinyUI.tabButtons) == "table" then
        for i, bt in ipairs(WanTinyUI.tabButtons) do
            if bt and bt.SetSelected then
                bt:SetSelected(i == tabIndex)
            end
        end
    end
    
    -- 控制标签页内容的显示/隐藏
    if WanTinyUI.tabContents then
        for i, content in ipairs(WanTinyUI.tabContents) do
            content:SetShown(i == tabIndex)
        end
    end

    -- 更新窗口标题文字
    if WanTinyUI.TabTitleLeft and WanTinyUI.TabTitleRight then
        WanTinyUI.TabTitleLeft:SetText(TAB_TITLES.left[tabIndex] or "")
        WanTinyUI.TabTitleRight:SetText(TAB_TITLES.right[tabIndex] or "")
    end
end

function WanTinyUI.ToggleMainFrame()
    if not WanTinyUI.MainFrame then
        WanTinyUI.Init()
        WanTinyUI.MainFrame:Show()
    else
        WanTinyUI.MainFrame:SetShown(not WanTinyUI.MainFrame:IsVisible())
    end
end

-- ==================== 事件处理 ====================
local frame = CreateFrame("Frame")
frame:RegisterEvent("ADDON_LOADED")
frame:SetScript("OnEvent", function(self, event, addonName)
    if event == "ADDON_LOADED" and addonName == "WanTiny" then
        -- 将WanTinyUI暴露到全局，供其他模块调用
        _G.WanTinyUI = WanTinyUI

        SLASH_WANTINYUI1 = "/wt"
        SlashCmdList["WANTINYUI"] = WanTinyUI.ToggleMainFrame

    end
end)