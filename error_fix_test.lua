-- WChat.lua 错误修复验证脚本
-- 用于验证修复后的代码是否能正常运行

local function TestErrorFixes()
    print("=== WChat.lua 错误修复验证开始 ===")
    
    local errors = {}
    local success = 0
    
    -- 测试1: ConfigManager对象是否正确定义
    if ConfigManager and type(ConfigManager) == "table" then
        print("✓ ConfigManager 对象定义正确")
        success = success + 1
        
        -- 测试ConfigManager方法
        if ConfigManager.GetConfig and type(ConfigManager.GetConfig) == "function" then
            print("✓ ConfigManager:GetConfig 方法存在")
            success = success + 1
        else
            table.insert(errors, "ConfigManager:GetConfig 方法缺失")
        end
        
        if ConfigManager.GetConfigSafe and type(ConfigManager.GetConfigSafe) == "function" then
            print("✓ ConfigManager:GetConfigSafe 方法存在")
            success = success + 1
        else
            table.insert(errors, "ConfigManager:GetConfigSafe 方法缺失")
        end
        
        if ConfigManager.HandleConfigChange and type(ConfigManager.HandleConfigChange) == "function" then
            print("✓ ConfigManager:HandleConfigChange 方法存在")
            success = success + 1
        else
            table.insert(errors, "ConfigManager:HandleConfigChange 方法缺失")
        end
    else
        table.insert(errors, "ConfigManager 对象未正确定义")
    end
    
    -- 测试2: ChatBarUtils对象是否正确定义
    if ChatBarUtils and type(ChatBarUtils) == "table" then
        print("✓ ChatBarUtils 对象定义正确")
        success = success + 1
        
        -- 测试ChatBarUtils方法
        if ChatBarUtils.SetupDragging and type(ChatBarUtils.SetupDragging) == "function" then
            print("✓ ChatBarUtils.SetupDragging 方法存在")
            success = success + 1
        else
            table.insert(errors, "ChatBarUtils.SetupDragging 方法缺失")
        end
        
        if ChatBarUtils.savePosition and type(ChatBarUtils.savePosition) == "function" then
            print("✓ ChatBarUtils.savePosition 方法存在")
            success = success + 1
        else
            table.insert(errors, "ChatBarUtils.savePosition 方法缺失")
        end
        
        if ChatBarUtils.setBackdrop and type(ChatBarUtils.setBackdrop) == "function" then
            print("✓ ChatBarUtils.setBackdrop 方法存在")
            success = success + 1
        else
            table.insert(errors, "ChatBarUtils.setBackdrop 方法缺失")
        end
    else
        table.insert(errors, "ChatBarUtils 对象未正确定义")
    end
    
    -- 测试3: GetConfig全局函数是否正确定义
    if GetConfig and type(GetConfig) == "function" then
        print("✓ GetConfig 全局函数定义正确")
        success = success + 1
    else
        table.insert(errors, "GetConfig 全局函数未正确定义")
    end
    
    -- 测试4: 尝试调用ConfigManager方法（如果可能）
    if ConfigManager and ConfigManager.GetConfig then
        local testSuccess, testResult = pcall(function()
            return ConfigManager:GetConfig()
        end)
        if testSuccess then
            print("✓ ConfigManager:GetConfig 调用成功")
            success = success + 1
        else
            table.insert(errors, "ConfigManager:GetConfig 调用失败: " .. tostring(testResult))
        end
    end
    
    -- 测试5: 检查向后兼容函数
    if SetupChatBarDragging and type(SetupChatBarDragging) == "function" then
        print("✓ SetupChatBarDragging 向后兼容函数存在")
        success = success + 1
    else
        table.insert(errors, "SetupChatBarDragging 向后兼容函数缺失")
    end
    
    print("")
    print("=== 修复验证结果 ===")
    print("成功项目:", success)
    print("错误项目:", #errors)
    
    if #errors == 0 then
        print("🎉 所有错误都已修复！")
    else
        print("⚠️  仍存在以下问题:")
        for i, error in ipairs(errors) do
            print("  " .. i .. ". " .. error)
        end
    end
    
    print("=== WChat.lua 错误修复验证完成 ===")
end

-- 延迟执行测试，确保所有模块都已加载
C_Timer.After(0.5, TestErrorFixes)

return TestErrorFixes
