-- WChat.lua 运行时错误测试脚本
-- 模拟实际运行环境来测试修复的错误

-- 模拟WoW API函数（如果不存在）
if not WChatClassicDB then WChatClassicDB = {} end
if not DEFAULT_CHAT_FRAME then 
    DEFAULT_CHAT_FRAME = {
        editBox = {
            ClearAllPoints = function() end,
            SetPoint = function() end,
            GetText = function() return "" end
        }
    }
end
if not UIParent then UIParent = {} end
if not CreateFrame then 
    CreateFrame = function() 
        return {
            SetMovable = function() end,
            EnableMouse = function() end,
            RegisterForDrag = function() end,
            SetScript = function() end,
            StartMoving = function() end,
            StopMovingOrSizing = function() end,
            GetPoint = function() return "CENTER", nil, "CENTER", 0, 0 end,
            SetBackdropColor = function() end,
            SetBackdropBorderColor = function() end
        }
    end 
end
if not C_Timer then
    C_Timer = {
        After = function(delay, func) func() end,
        NewTimer = function(delay, func) return {Cancel = function() end} end
    }
end

local function TestRuntimeErrors()
    print("=== WChat.lua 运行时错误测试开始 ===")
    
    local testResults = {}
    
    -- 测试1: ConfigManager调用
    local function testConfigManager()
        local success, result = pcall(function()
            if ConfigManager and ConfigManager.GetConfig then
                local config = ConfigManager:GetConfig()
                return config ~= nil
            end
            return false
        end)
        return success, result
    end
    
    local success1, result1 = testConfigManager()
    testResults["ConfigManager:GetConfig"] = {success = success1, result = result1}
    if success1 then
        print("✓ ConfigManager:GetConfig 调用成功")
    else
        print("✗ ConfigManager:GetConfig 调用失败:", result1)
    end
    
    -- 测试2: ChatBarUtils.SetupDragging调用
    local function testChatBarUtils()
        local success, result = pcall(function()
            if ChatBarUtils and ChatBarUtils.SetupDragging then
                -- 创建一个模拟的聊天条
                local mockChatBar = {
                    SetMovable = function() end,
                    EnableMouse = function() end,
                    RegisterForDrag = function() end,
                    SetScript = function() end
                }
                ChatBarUtils.SetupDragging(mockChatBar, true)
                return true
            end
            return false
        end)
        return success, result
    end
    
    local success2, result2 = testChatBarUtils()
    testResults["ChatBarUtils.SetupDragging"] = {success = success2, result = result2}
    if success2 then
        print("✓ ChatBarUtils.SetupDragging 调用成功")
    else
        print("✗ ChatBarUtils.SetupDragging 调用失败:", result2)
    end
    
    -- 测试3: GetConfig全局函数调用
    local function testGetConfig()
        local success, result = pcall(function()
            if GetConfig then
                local config = GetConfig()
                return config ~= nil
            end
            return false
        end)
        return success, result
    end
    
    local success3, result3 = testGetConfig()
    testResults["GetConfig"] = {success = success3, result = result3}
    if success3 then
        print("✓ GetConfig 全局函数调用成功")
    else
        print("✗ GetConfig 全局函数调用失败:", result3)
    end
    
    -- 测试4: ConfigHandlers.LockChatBar调用
    local function testConfigHandlers()
        local success, result = pcall(function()
            if ConfigHandlers and ConfigHandlers.LockChatBar then
                -- 模拟WChat.ChatBar
                if not WChat then WChat = {} end
                WChat.ChatBar = {
                    SetMovable = function() end,
                    EnableMouse = function() end,
                    RegisterForDrag = function() end,
                    SetScript = function() end,
                    SetBackdropColor = function() end,
                    SetBackdropBorderColor = function() end
                }
                ConfigHandlers.LockChatBar(true)
                return true
            end
            return false
        end)
        return success, result
    end
    
    local success4, result4 = testConfigHandlers()
    testResults["ConfigHandlers.LockChatBar"] = {success = success4, result = result4}
    if success4 then
        print("✓ ConfigHandlers.LockChatBar 调用成功")
    else
        print("✗ ConfigHandlers.LockChatBar 调用失败:", result4)
    end
    
    -- 统计结果
    local totalTests = 0
    local passedTests = 0
    for testName, result in pairs(testResults) do
        totalTests = totalTests + 1
        if result.success then
            passedTests = passedTests + 1
        end
    end
    
    print("")
    print("=== 运行时错误测试结果 ===")
    print(string.format("通过: %d/%d (%.1f%%)", passedTests, totalTests, (passedTests/totalTests)*100))
    
    if passedTests == totalTests then
        print("🎉 所有运行时错误都已修复！")
    else
        print("⚠️  仍有", totalTests - passedTests, "个测试失败")
    end
    
    print("=== WChat.lua 运行时错误测试完成 ===")
end

-- 延迟执行测试
C_Timer.After(1, TestRuntimeErrors)

return TestRuntimeErrors
