-- WChat.lua 语法检查脚本
-- 用于验证优化后的代码语法正确性

local function CheckWChatSyntax()
    print("=== WChat.lua 语法检查开始 ===")
    
    -- 检查主要对象是否正确定义
    local checks = {
        {name = "CONFIG", obj = CONFIG, desc = "配置常量对象"},
        {name = "WChat", obj = WChat, desc = "主模块对象"},
        {name = "ConfigManager", obj = ConfigManager, desc = "配置管理器"},
        {name = "Utils", obj = Utils, desc = "工具函数库"},
        {name = "EventManager", obj = EventManager, desc = "事件管理器"},
        {name = "TimerManager", obj = TimerManager, desc = "定时器管理器"},
        {name = "WhisperDBManager", obj = WhisperDBManager, desc = "密语数据库管理器"},
        {name = "WhisperUIUtils", obj = WhisperUIUtils, desc = "密语UI工具"},
        {name = "ChannelManager", obj = ChannelManager, desc = "频道管理器"},
        {name = "ClickHandlers", obj = ClickHandlers, desc = "点击处理器"},
        {name = "ButtonStateManager", obj = ButtonStateManager, desc = "按钮状态管理器"},
        {name = "ChatBarInitializer", obj = ChatBarInitializer, desc = "聊天条初始化器"}
    }
    
    local passCount = 0
    local totalCount = #checks
    
    for _, check in ipairs(checks) do
        if check.obj and type(check.obj) == "table" then
            print("✓", check.name, "-", check.desc, "定义正确")
            passCount = passCount + 1
        else
            print("✗", check.name, "-", check.desc, "定义异常或缺失")
        end
    end
    
    -- 检查关键函数是否存在
    local functionChecks = {
        {name = "WChat:Cleanup", func = WChat and WChat.Cleanup, desc = "清理函数"},
        {name = "WChat:InitChatBar", func = WChat and WChat.InitChatBar, desc = "聊天条初始化"},
        {name = "ConfigManager.GetConfig", func = ConfigManager and ConfigManager.GetConfig, desc = "配置获取函数"},
        {name = "EventManager:CreateFrame", func = EventManager and EventManager.CreateFrame, desc = "事件框架创建"},
        {name = "TimerManager:DelayedCall", func = TimerManager and TimerManager.DelayedCall, desc = "延迟调用函数"},
        {name = "WhisperDBManager:GetGlobalDB", func = WhisperDBManager and WhisperDBManager.GetGlobalDB, desc = "数据库获取函数"}
    }
    
    for _, check in ipairs(functionChecks) do
        if check.func and type(check.func) == "function" then
            print("✓", check.name, "-", check.desc, "存在")
            passCount = passCount + 1
        else
            print("✗", check.name, "-", check.desc, "缺失或类型错误")
        end
    end
    
    totalCount = totalCount + #functionChecks
    
    -- 检查模块注册
    if WanTiny_RegisterModule then
        print("✓ WanTiny模块注册系统可用")
        passCount = passCount + 1
    else
        print("✗ WanTiny模块注册系统不可用")
    end
    totalCount = totalCount + 1
    
    -- 检查全局暴露
    if _G.WChat then
        print("✓ WChat全局对象已正确暴露")
        passCount = passCount + 1
    else
        print("✗ WChat全局对象未正确暴露")
    end
    totalCount = totalCount + 1
    
    print("")
    print("=== 语法检查结果 ===")
    print(string.format("通过: %d/%d (%.1f%%)", passCount, totalCount, (passCount/totalCount)*100))
    
    if passCount == totalCount then
        print("🎉 所有检查项目都通过！代码语法正确。")
    else
        print("⚠️  存在", totalCount - passCount, "个问题需要修复。")
    end
    
    print("=== WChat.lua 语法检查完成 ===")
end

-- 延迟执行检查，确保所有模块都已加载
C_Timer.After(1, CheckWChatSyntax)

return CheckWChatSyntax
