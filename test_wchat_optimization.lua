-- WChat优化后功能测试脚本
-- 用于验证优化后的WChat模块功能完整性

local TestWChat = {}

-- 测试配置管理系统
function TestWChat:TestConfigManager()
    print("测试配置管理系统...")
    
    -- 测试配置获取
    local config = ConfigManager and ConfigManager.GetConfig()
    if config then
        print("✓ 配置管理器正常工作")
        print("  - 默认配置加载:", config.UseTopInput and "是" or "否")
        print("  - 按钮大小:", config.ButtonSize or "未设置")
    else
        print("✗ 配置管理器未正常工作")
    end
end

-- 测试事件管理系统
function TestWChat:TestEventManager()
    print("测试事件管理系统...")
    
    if EventManager then
        print("✓ 事件管理器存在")
        
        -- 测试创建框架
        local testFrame = EventManager:CreateFrame("test")
        if testFrame then
            print("✓ 事件框架创建成功")
            
            -- 测试事件注册
            local eventTriggered = false
            EventManager:RegisterEvent("test", "ADDON_LOADED", function()
                eventTriggered = true
            end)
            print("✓ 事件注册成功")
            
            -- 清理测试
            EventManager:CleanupFrame("test")
            print("✓ 事件清理成功")
        else
            print("✗ 事件框架创建失败")
        end
    else
        print("✗ 事件管理器不存在")
    end
end

-- 测试定时器管理系统
function TestWChat:TestTimerManager()
    print("测试定时器管理系统...")
    
    if TimerManager then
        print("✓ 定时器管理器存在")
        
        -- 测试延迟调用
        local callbackExecuted = false
        TimerManager:DelayedCall("test", function()
            callbackExecuted = true
            print("✓ 延迟调用执行成功")
        end, 0.1)
        
        -- 测试定时器存在检查
        if TimerManager:Exists("test") then
            print("✓ 定时器存在检查正常")
        else
            print("✗ 定时器存在检查失败")
        end
        
        -- 测试取消定时器
        TimerManager:Cancel("test")
        if not TimerManager:Exists("test") then
            print("✓ 定时器取消成功")
        else
            print("✗ 定时器取消失败")
        end
    else
        print("✗ 定时器管理器不存在")
    end
end

-- 测试密语数据库管理器
function TestWChat:TestWhisperDBManager()
    print("测试密语数据库管理器...")
    
    if WhisperDBManager then
        print("✓ 密语数据库管理器存在")
        
        -- 测试获取全局数据库
        local globalDB = WhisperDBManager:GetGlobalDB()
        if globalDB then
            print("✓ 全局数据库获取成功")
            print("  - 开启状态:", globalDB.Open and "是" or "否")
            print("  - 声音提醒:", globalDB.SoundAlert and "是" or "否")
            print("  - 保存天数:", globalDB.Days or "未设置")
        else
            print("✗ 全局数据库获取失败")
        end
        
        -- 测试缓存清理
        WhisperDBManager:ClearCache()
        print("✓ 缓存清理成功")
    else
        print("✗ 密语数据库管理器不存在")
    end
end

-- 测试UI工具系统
function TestWChat:TestWhisperUIUtils()
    print("测试密语UI工具系统...")
    
    if WhisperUIUtils then
        print("✓ 密语UI工具系统存在")
        
        -- 测试获取职业颜色
        local r, g, b = WhisperUIUtils:GetClassRGB()
        if r and g and b then
            print("✓ 职业颜色获取成功:", string.format("RGB(%.2f, %.2f, %.2f)", r, g, b))
        else
            print("✗ 职业颜色获取失败")
        end
    else
        print("✗ 密语UI工具系统不存在")
    end
end

-- 测试Utils工具函数
function TestWChat:TestUtils()
    print("测试Utils工具函数...")
    
    if Utils then
        print("✓ Utils工具库存在")
        
        -- 测试字符串处理
        local testName = "TestPlayer-Server"
        local displayName = Utils.GetDisplayName(testName)
        if displayName == "TestPlayer" then
            print("✓ 显示名称处理正常")
        else
            print("✗ 显示名称处理异常:", displayName)
        end
        
        -- 测试职业颜色代码
        local colorCode = Utils.GetClassColorCode("WARRIOR")
        if colorCode and colorCode:find("|c") then
            print("✓ 职业颜色代码生成正常")
        else
            print("✗ 职业颜色代码生成异常")
        end
    else
        print("✗ Utils工具库不存在")
    end
end

-- 测试WChat主模块
function TestWChat:TestWChatModule()
    print("测试WChat主模块...")
    
    if _G.WChat then
        print("✓ WChat主模块存在")
        
        -- 测试配置变化处理器
        if WChat.OnConfigChanged then
            print("✓ 配置变化处理器存在")
            print("  - 处理器数量:", Utils.TableLength(WChat.OnConfigChanged))
        else
            print("✗ 配置变化处理器不存在")
        end
        
        -- 测试清理函数
        if WChat.Cleanup then
            print("✓ 清理函数存在")
        else
            print("✗ 清理函数不存在")
        end
    else
        print("✗ WChat主模块不存在")
    end
end

-- 运行所有测试
function TestWChat:RunAllTests()
    print("=== WChat优化后功能测试开始 ===")
    print("")
    
    self:TestConfigManager()
    print("")
    
    self:TestEventManager()
    print("")
    
    self:TestTimerManager()
    print("")
    
    self:TestWhisperDBManager()
    print("")
    
    self:TestWhisperUIUtils()
    print("")
    
    self:TestUtils()
    print("")
    
    self:TestWChatModule()
    print("")
    
    print("=== WChat优化后功能测试完成 ===")
end

-- 延迟执行测试，确保WChat模块已加载
if WanTiny_RegisterModule then
    WanTiny_RegisterModule("WChatTest", function()
        C_Timer.After(2, function()
            TestWChat:RunAllTests()
        end)
    end)
else
    -- 如果没有WanTiny，直接执行测试
    C_Timer.After(3, function()
        TestWChat:RunAllTests()
    end)
end

return TestWChat
