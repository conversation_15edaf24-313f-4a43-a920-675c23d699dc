-- WanTiny 主设置模块
local addonName, addon = ...
local VERSION = "2.0.0"

-- 模块注册系统
local registeredModules = {}  -- 存储已注册的模块
local initializedModules = {} -- 存储已初始化的模块

-- 简化打印函数
local function Print(msg)
    print("|cFF00CCFF[晚妹的小盒子]|r " .. msg)
end

-- 模块描述表已移至 WanTinyUI.lua，此处不再需要

-- 默认配置
local defaultConfig = {
    -- 各模块的启用/禁用状态
    enabledModules = {
        BuffTimers = true,
        idTip = true,
        Focuser = true,
        RaidInfoFrame = true,
        RaidCD = true,
        WanRightClick = true,
        iColor = true,
        StatusInfo = true,
        WanOutOfRange = true,
        QuestPrice = true,
        AutoProfiler = true,
        WCvar = true,
        MHQuickButton = true,
        BannerHider = true,
        Buttonstorage = true,
        WanMenu = true,
        WCombatTimes = true,
        WChat = true
    },
    -- 面板设置
    panelSettings = {
        showTitle = true,
        showVersion = true
    }
}

-- 创建或获取全局保存变量
_G.WanTinyDB = _G.WanTinyDB or {}
WanTinyDB.config = WanTinyDB.config or CopyTable(defaultConfig)

-- 安全合并配置函数 - 只添加缺少的键，不覆盖现有值
local function MergeConfigs(target, source)
    for k, v in pairs(source) do
        if target[k] == nil then
            target[k] = v
        elseif type(v) == "table" and type(target[k]) == "table" then
            MergeConfigs(target[k], v)
        end
    end
end

-- 配置管理辅助函数
local configManager = {
    SaveConfig = function()
        if _G.WanTinyDB and _G.WanTinyDB.config then
            local tempConfig = CopyTable(_G.WanTinyDB.config)
            _G.WanTinyDB.config = tempConfig
            return true
        end
        return false
    end,

    LoadConfig = function()
        _G.WanTinyDB = _G.WanTinyDB or {}
        _G.WanTinyDB.config = _G.WanTinyDB.config or CopyTable(defaultConfig)
        MergeConfigs(_G.WanTinyDB.config, defaultConfig)
        return true
    end,
    
    -- 重置所有配置
    ResetConfig = function()
        _G.WanTinyDB.config = CopyTable(defaultConfig)
        Print("所有设置已重置为默认值")
        return true
    end
}

-- 初始化时加载配置
configManager.LoadConfig()

-- 注册新模块的函数，会被子模块调用
function WanTiny_RegisterModule(moduleName, initFunc)
    if not moduleName or type(initFunc) ~= "function" then return end
    
    registeredModules[moduleName] = initFunc
    
    if addon.initialized and addon:IsModuleEnabled(moduleName) then
        if not initializedModules[moduleName] then
            initializedModules[moduleName] = true
            initFunc()
        end
    end
end

-- 提供一个公共的API来检查模块是否启用
function addon:IsModuleEnabled(moduleName)
    return WanTinyDB.config.enabledModules[moduleName] ~= false
end

-- 提供一个API来启用或禁用模块
function addon:SetModuleEnabled(moduleName, enabled)
    if WanTinyDB.config.enabledModules[moduleName] ~= enabled then
        WanTinyDB.config.enabledModules[moduleName] = enabled
        return true
    end
    return false
end

-- 事件处理
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("ADDON_LOADED")
eventFrame:RegisterEvent("PLAYER_LOGIN")
eventFrame:RegisterEvent("PLAYER_LOGOUT")

eventFrame:SetScript("OnEvent", function(self, event, arg1)
    if event == "ADDON_LOADED" and arg1 == addonName then
    elseif event == "PLAYER_LOGIN" then
        addon.initialized = true
        
        -- 初始化已注册的模块
        local loadedCount = 0
        for moduleName, initFunc in pairs(registeredModules) do
            if addon:IsModuleEnabled(moduleName) and not initializedModules[moduleName] then
                initializedModules[moduleName] = true
                initFunc()
                loadedCount = loadedCount + 1
            end
        end
        
        
        self:UnregisterEvent("PLAYER_LOGIN")
        self:UnregisterEvent("ADDON_LOADED")
    elseif event == "PLAYER_LOGOUT" then
        configManager.SaveConfig()
    end
end)
